<?php
function get_registration_email_template($first_name, $last_name, $nickname, $username, $gender, $birthday, $language, $email, $phone_number) {
    $gender_text = ($gender == 'male') ? 'Male' : 'Female';
    $language_map = [
        'en' => 'English',
        'zh' => '中文',
        'es' => 'Español',
        'ja' => '日本語',
        'ko' => '한국어',
        'other' => 'Other'
    ];
    $language_text = isset($language_map[$language]) ? $language_map[$language] : 'Not specified';

    return <<<HTML
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Welcome to KelvinKMS.com!</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background-color: #f4f4f4;
            margin: 0;
            padding: 0;
            color: #333;
        }
        .container {
            width: 100%;
            max-width: 600px;
            margin: 0 auto;
            background-color: #ffffff;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #ffffff;
            padding: 40px;
            text-align: center;
        }
        .header h1 {
            margin: 0;
            font-size: 28px;
        }
        .content {
            padding: 30px;
        }
        .content h2 {
            color: #764ba2;
        }
        .content p {
            line-height: 1.6;
        }
        .user-details {
            background-color: #f9f9f9;
            border-left: 4px solid #667eea;
            padding: 20px;
            margin: 20px 0;
        }
        .user-details th, .user-details td {
            padding: 8px 0;
            text-align: left;
        }
        .user-details th {
            color: #666;
            width: 150px;
        }
        .footer {
            background-color: #333;
            color: #bbb;
            text-align: center;
            padding: 20px;
            font-size: 12px;
        }
        .footer a {
            color: #667eea;
            text-decoration: none;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Welcome to KelvinKMS.com!</h1>
        </div>
        <div class="content">
            <h2>Hi {$nickname},</h2>
            <p>Thank you for registering at KelvinKMS.com. We are excited to have you as a member of our community!</p>
            <p>Here is a summary of your registration details:</p>
            <div class="user-details">
                <table>
                    <tr><th>First Name:</th><td>{$first_name}</td></tr>
                    <tr><th>Last Name:</th><td>{$last_name}</td></tr>
                    <tr><th>Nickname:</th><td>{$nickname}</td></tr>
                    <tr><th>Username:</th><td>{$username}</td></tr>
                    <tr><th>Gender:</th><td>{$gender_text}</td></tr>
                    <tr><th>Birthday:</th><td>{$birthday}</td></tr>
                    <tr><th>Language:</th><td>{$language_text}</td></tr>
                    <tr><th>Email:</th><td>{$email}</td></tr>
                    <tr><th>Phone Number:</th><td>{$phone_number}</td></tr>
                </table>
            </div>
            <p>We recommend you keep this email for your records. If you have any questions, feel free to contact us.</p>
            <p>Best regards,<br>The KelvinKMS.com Team</p>
        </div>
        <div class="footer">
            <p>&copy; 2025 KelvinKMS.com. All rights reserved.</p>
            <p>If you did not register for this account, please ignore this email.</p>
        </div>
    </div>
</body>
</html>
HTML;
}
?>