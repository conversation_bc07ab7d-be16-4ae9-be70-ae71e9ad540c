<?php
/**
 * Stripe Payment Callback Handler
 * This handles Stripe payment processing and updates deposit status
 */

require_once 'config.php';
require_once 'credit_system.php';

header('Content-Type: application/json');

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    echo json_encode(['success' => false, 'message' => 'User not logged in']);
    exit;
}

$action = $_POST['action'] ?? '';

switch ($action) {
    case 'simulate_stripe_success':
        simulateStripeSuccess();
        break;
        
    case 'process_stripe_payment':
        processStripePayment();
        break;
        
    case 'handle_stripe_webhook':
        handleStripeWebhook();
        break;
        
    default:
        echo json_encode(['success' => false, 'message' => 'Invalid action']);
        break;
}

/**
 * Simulate successful Stripe payment (for demo purposes)
 */
function simulateStripeSuccess() {
    global $credit_system, $link;
    
    $transaction_id = $_POST['transaction_id'] ?? '';
    $external_payment_id = $_POST['external_payment_id'] ?? '';
    
    if (empty($transaction_id)) {
        echo json_encode(['success' => false, 'message' => 'Transaction ID required']);
        return;
    }
    
    // Get deposit details
    $sql = "SELECT dr.*, ct.user_id FROM deposit_records dr 
            JOIN credit_transactions ct ON dr.transaction_id = ct.transaction_id 
            WHERE dr.transaction_id = ? AND dr.user_id = ?";
    $stmt = execute_query($link, $sql, "si", [$transaction_id, $_SESSION['user_id']]);
    
    if (!$stmt) {
        echo json_encode(['success' => false, 'message' => 'Database error']);
        return;
    }
    
    $result = mysqli_stmt_get_result($stmt);
    $deposit = mysqli_fetch_assoc($result);
    mysqli_stmt_close($stmt);
    
    if (!$deposit) {
        echo json_encode(['success' => false, 'message' => 'Deposit not found']);
        return;
    }
    
    if ($deposit['payment_status'] !== 'pending') {
        echo json_encode(['success' => false, 'message' => 'Deposit already processed']);
        return;
    }
    
    mysqli_begin_transaction($link);
    
    try {
        // Add credit to user's wallet
        $add_result = $credit_system->addCredit(
            $deposit['user_id'],
            $deposit['amount'],
            'stripe',
            'Stripe payment completed',
            $external_payment_id
        );
        
        if (!$add_result['success']) {
            throw new Exception($add_result['message']);
        }
        
        // Update deposit status
        $update_sql = "UPDATE deposit_records SET payment_status = 'completed', external_payment_id = ?, payment_details = ? WHERE transaction_id = ?";
        $payment_details = json_encode([
            'payment_method' => 'stripe',
            'external_id' => $external_payment_id,
            'completed_at' => date('Y-m-d H:i:s'),
            'demo_mode' => true
        ]);
        
        $update_stmt = execute_query($link, $update_sql, "sss", [$external_payment_id, $payment_details, $transaction_id]);
        
        if (!$update_stmt) {
            throw new Exception('Failed to update deposit status');
        }
        mysqli_stmt_close($update_stmt);
        
        mysqli_commit($link);
        
        echo json_encode([
            'success' => true,
            'message' => 'Payment processed successfully',
            'new_balance' => $add_result['new_balance']
        ]);
        
    } catch (Exception $e) {
        mysqli_rollback($link);
        echo json_encode(['success' => false, 'message' => $e->getMessage()]);
    }
}

/**
 * Process real Stripe payment
 */
function processStripePayment() {
    global $credit_system, $link;
    
    $transaction_id = $_POST['transaction_id'] ?? '';
    $payment_method_id = $_POST['payment_method_id'] ?? '';
    
    if (empty($transaction_id) || empty($payment_method_id)) {
        echo json_encode(['success' => false, 'message' => 'Missing required parameters']);
        return;
    }
    
    // Get deposit details
    $sql = "SELECT dr.*, ct.user_id FROM deposit_records dr 
            JOIN credit_transactions ct ON dr.transaction_id = ct.transaction_id 
            WHERE dr.transaction_id = ? AND dr.user_id = ?";
    $stmt = execute_query($link, $sql, "si", [$transaction_id, $_SESSION['user_id']]);
    
    if (!$stmt) {
        echo json_encode(['success' => false, 'message' => 'Database error']);
        return;
    }
    
    $result = mysqli_stmt_get_result($stmt);
    $deposit = mysqli_fetch_assoc($result);
    mysqli_stmt_close($stmt);
    
    if (!$deposit) {
        echo json_encode(['success' => false, 'message' => 'Deposit not found']);
        return;
    }
    
    // In real implementation, you would:
    // 1. Initialize Stripe with secret key
    // 2. Create payment intent
    // 3. Confirm payment
    // 4. Handle result
    
    /*
    require_once 'vendor/autoload.php';
    \Stripe\Stripe::setApiKey('sk_test_your_secret_key_here');
    
    try {
        $intent = \Stripe\PaymentIntent::create([
            'amount' => $deposit['amount'] * 100, // Amount in cents
            'currency' => 'usd',
            'payment_method' => $payment_method_id,
            'confirmation_method' => 'manual',
            'confirm' => true,
            'metadata' => [
                'transaction_id' => $transaction_id,
                'user_id' => $deposit['user_id']
            ]
        ]);
        
        if ($intent->status === 'succeeded') {
            // Payment successful, add credit to user account
            $add_result = $credit_system->addCredit(
                $deposit['user_id'],
                $deposit['amount'],
                'stripe',
                'Stripe payment completed',
                $intent->id
            );
            
            if ($add_result['success']) {
                // Update deposit status
                $update_sql = "UPDATE deposit_records SET payment_status = 'completed', external_payment_id = ? WHERE transaction_id = ?";
                execute_query($link, $update_sql, "ss", [$intent->id, $transaction_id]);
                
                echo json_encode([
                    'success' => true,
                    'message' => 'Payment successful',
                    'new_balance' => $add_result['new_balance']
                ]);
            } else {
                echo json_encode(['success' => false, 'message' => 'Failed to add credit']);
            }
        } else {
            echo json_encode(['success' => false, 'message' => 'Payment failed']);
        }
        
    } catch (\Stripe\Exception\CardException $e) {
        echo json_encode(['success' => false, 'message' => $e->getError()->message]);
    } catch (Exception $e) {
        echo json_encode(['success' => false, 'message' => 'Payment processing error']);
    }
    */
    
    // For demo, just return success
    echo json_encode(['success' => true, 'message' => 'Demo: Payment would be processed here']);
}

/**
 * Handle Stripe webhook events
 */
function handleStripeWebhook() {
    // In real implementation, you would:
    // 1. Verify webhook signature
    // 2. Parse webhook event
    // 3. Handle different event types
    // 4. Update payment status accordingly
    
    /*
    $payload = @file_get_contents('php://input');
    $sig_header = $_SERVER['HTTP_STRIPE_SIGNATURE'];
    $endpoint_secret = 'whsec_your_webhook_secret_here';
    
    try {
        $event = \Stripe\Webhook::constructEvent(
            $payload, $sig_header, $endpoint_secret
        );
        
        switch ($event['type']) {
            case 'payment_intent.succeeded':
                $payment_intent = $event['data']['object'];
                $transaction_id = $payment_intent['metadata']['transaction_id'];
                
                // Update deposit status and add credit
                // ... implementation here
                
                break;
                
            case 'payment_intent.payment_failed':
                $payment_intent = $event['data']['object'];
                $transaction_id = $payment_intent['metadata']['transaction_id'];
                
                // Mark deposit as failed
                // ... implementation here
                
                break;
                
            default:
                // Unhandled event type
                break;
        }
        
        http_response_code(200);
        echo json_encode(['success' => true]);
        
    } catch (\UnexpectedValueException $e) {
        // Invalid payload
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => 'Invalid payload']);
    } catch (\Stripe\Exception\SignatureVerificationException $e) {
        // Invalid signature
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => 'Invalid signature']);
    }
    */
    
    echo json_encode(['success' => true, 'message' => 'Webhook processed']);
}
?>

<!-- Stripe Integration Documentation -->
<!--
To implement real Stripe integration:

1. Set up Stripe Account:
   - Go to https://stripe.com/
   - Create account and get API keys
   - Set up webhooks

2. Install Stripe PHP SDK:
   composer require stripe/stripe-php

3. Basic Payment Flow:
   
   Frontend (JavaScript):
   const stripe = Stripe('pk_test_your_publishable_key');
   const elements = stripe.elements();
   const cardElement = elements.create('card');
   
   // Create payment method
   const {paymentMethod, error} = await stripe.createPaymentMethod({
       type: 'card',
       card: cardElement,
   });
   
   Backend (PHP):
   \Stripe\Stripe::setApiKey('sk_test_your_secret_key');
   
   $intent = \Stripe\PaymentIntent::create([
       'amount' => $amount * 100, // cents
       'currency' => 'usd',
       'payment_method' => $payment_method_id,
       'confirm' => true,
   ]);

4. Webhook Setup:
   - Configure webhook endpoint in Stripe dashboard
   - Handle events like payment_intent.succeeded
   - Verify webhook signatures for security

5. Error Handling:
   - Handle card declined, insufficient funds, etc.
   - Provide user-friendly error messages
   - Log errors for debugging

6. Testing:
   - Use test card numbers: ****************
   - Test different scenarios (success, decline, etc.)
   - Use Stripe CLI for webhook testing
-->
