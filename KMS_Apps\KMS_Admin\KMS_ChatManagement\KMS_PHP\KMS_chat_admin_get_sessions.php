<?php
header('Content-Type: application/json');
require_once __DIR__ . '/config.php';

// --- Admin Section - Security Check ---
// This is a placeholder for a real admin check. In a real application,
// you would have a more robust role-based access control.
if (!isset($_SESSION['is_admin']) || $_SESSION['is_admin'] !== true) {
    http_response_code(403); // Forbidden
    echo json_encode(['success' => false, 'message' => 'Access denied. Administrator privileges required.']);
    exit;
}

$response = ['success' => false, 'sessions' => []];
$link = get_db_connection();

try {
    // Fetch all pending and active chat sessions
    // Order by status (pending first) and then by creation date
    $sql = "
        SELECT 
            cs.id, 
            cs.session_guid, 
            cs.user_id, 
            cs.guest_nickname, 
            cs.status,
            cs.created_at,
            (SELECT COUNT(*) FROM chat_messages cm WHERE cm.chat_session_id = cs.id AND cm.sender = 'user' AND cm.is_read = 0) as unread_messages
        FROM 
            chat_sessions cs
        WHERE 
            cs.status IN ('pending', 'active')
        ORDER BY 
            FIELD(cs.status, 'pending', 'active'), 
            cs.created_at ASC
    ";

    $result = mysqli_query($link, $sql);
    if (!$result) {
        throw new Exception('Failed to fetch chat sessions: ' . mysqli_error($link));
    }

    $sessions = [];
    while ($row = mysqli_fetch_assoc($result)) {
        $sessions[] = $row;
    }
    
    $response['success'] = true;
    $response['sessions'] = $sessions;

} catch (Exception $e) {
    $response['message'] = $e->getMessage();
    error_log("Chat Admin Get Sessions Error: " . $e->getMessage());
} finally {
    if ($link) {
        close_db_connection($link);
    }
}

echo json_encode($response);
?>