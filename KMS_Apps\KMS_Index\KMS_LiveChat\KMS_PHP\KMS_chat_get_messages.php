<?php
header('Content-Type: application/json');
require_once __DIR__ . '/config.php';

$response = ['success' => false, 'messages' => []];
$link = get_db_connection();

try {
    $session_guid = $_GET['session_guid'] ?? null;
    $last_message_id = isset($_GET['last_message_id']) ? (int)$_GET['last_message_id'] : 0;

    if (empty($session_guid)) {
        throw new Exception('Session GUID is required.');
    }

    // Get the chat session ID from the GUID
    $sql_get_id = "SELECT id FROM chat_sessions WHERE session_guid = ?";
    $stmt_get_id = mysqli_prepare($link, $sql_get_id);
    mysqli_stmt_bind_param($stmt_get_id, "s", $session_guid);
    mysqli_stmt_execute($stmt_get_id);
    $result_id = mysqli_stmt_get_result($stmt_get_id);
    $session = mysqli_fetch_assoc($result_id);
    mysqli_stmt_close($stmt_get_id);

    if (!$session) {
        throw new Exception('Chat session not found.');
    }
    $chat_session_id = $session['id'];

    // Fetch new messages
    $sql_get_msgs = "SELECT id, sender, message, created_at FROM chat_messages WHERE chat_session_id = ? AND id > ? ORDER BY id ASC";
    $stmt_get_msgs = mysqli_prepare($link, $sql_get_msgs);
    
    if (!$stmt_get_msgs) {
        throw new Exception('Failed to prepare statement: ' . mysqli_error($link));
    }

    mysqli_stmt_bind_param($stmt_get_msgs, "ii", $chat_session_id, $last_message_id);
    mysqli_stmt_execute($stmt_get_msgs);
    $result_msgs = mysqli_stmt_get_result($stmt_get_msgs);

    $messages = [];
    while ($row = mysqli_fetch_assoc($result_msgs)) {
        $messages[] = $row;
    }
    mysqli_stmt_close($stmt_get_msgs);

    $response['success'] = true;
    $response['messages'] = $messages;

} catch (Exception $e) {
    $response['message'] = $e->getMessage();
    error_log("Chat Get Messages Error: " . $e->getMessage());
} finally {
    if ($link) {
        close_db_connection($link);
    }
}

echo json_encode($response);
?>