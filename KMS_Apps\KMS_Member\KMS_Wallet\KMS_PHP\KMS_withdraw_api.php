<?php
/**
 * KMS Withdrawal API
 * Handles withdrawal requests and processing
 */

session_start();
require_once 'config.php';
require_once 'credit_system.php';

// Check if user is logged in
if (!isset($_SESSION['id'])) {
    echo json_encode(['success' => false, 'message' => 'User not logged in']);
    exit;
}

$user_id = $_SESSION['id'];
$action = $_POST['action'] ?? $_GET['action'] ?? '';

header('Content-Type: application/json');

switch ($action) {
    case 'get_withdrawal_methods':
        getWithdrawalMethods();
        break;
        
    case 'create_withdrawal':
        createWithdrawal($credit_system, $user_id);
        break;
        
    case 'get_withdrawal_history':
        getWithdrawalHistory($user_id);
        break;
        
    case 'cancel_withdrawal':
        cancelWithdrawal($user_id);
        break;
        
    case 'get_withdrawal_limits':
        getWithdrawalLimits($user_id);
        break;
        
    default:
        echo json_encode(['success' => false, 'message' => 'Invalid action']);
        break;
}

/**
 * Get available withdrawal methods
 */
function getWithdrawalMethods() {
    $methods = [
        [
            'id' => 'paypal',
            'name' => 'PayPal',
            'display_name' => 'PayPal',
            'min_amount' => 25.00,
            'max_amount' => 2000.00,
            'processing_time' => '1-3 business days',
            'fee_percentage' => 1.0,
            'fee_fixed' => 0.0,
            'is_active' => true,
            'description' => 'Withdraw to your PayPal account',
            'required_fields' => ['paypal_email']
        ],
        [
            'id' => 'bank_transfer',
            'name' => 'Bank Transfer',
            'display_name' => 'Bank Transfer',
            'min_amount' => 50.00,
            'max_amount' => 5000.00,
            'processing_time' => '3-5 business days',
            'fee_percentage' => 0.0,
            'fee_fixed' => 2.50,
            'is_active' => true,
            'description' => 'Direct bank transfer (ACH)',
            'required_fields' => ['account_number', 'routing_number', 'account_holder_name', 'bank_name']
        ],
        [
            'id' => 'crypto',
            'name' => 'Cryptocurrency',
            'display_name' => 'Bitcoin/Ethereum',
            'min_amount' => 100.00,
            'max_amount' => 10000.00,
            'processing_time' => '30 minutes - 2 hours',
            'fee_percentage' => 2.0,
            'fee_fixed' => 0.0,
            'is_active' => true,
            'description' => 'Withdraw to crypto wallet',
            'required_fields' => ['crypto_type', 'wallet_address']
        ],
        [
            'id' => 'check',
            'name' => 'Check',
            'display_name' => 'Paper Check',
            'min_amount' => 100.00,
            'max_amount' => 3000.00,
            'processing_time' => '7-10 business days',
            'fee_percentage' => 0.0,
            'fee_fixed' => 5.00,
            'is_active' => true,
            'description' => 'Mailed paper check',
            'required_fields' => ['full_name', 'address', 'city', 'state', 'zip_code']
        ]
    ];
    
    echo json_encode([
        'success' => true,
        'withdrawal_methods' => $methods
    ]);
}

/**
 * Create a new withdrawal request
 */
function createWithdrawal($credit_system, $user_id) {
    $amount = floatval($_POST['amount'] ?? 0);
    $withdrawal_method = trim($_POST['withdrawal_method'] ?? '');
    $withdrawal_details = $_POST['withdrawal_details'] ?? [];
    $notes = trim($_POST['notes'] ?? '');
    
    if ($amount <= 0) {
        echo json_encode(['success' => false, 'message' => 'Invalid withdrawal amount']);
        return;
    }
    
    if (empty($withdrawal_method)) {
        echo json_encode(['success' => false, 'message' => 'Withdrawal method is required']);
        return;
    }
    
    // Validate withdrawal details
    $validation_result = validateWithdrawalDetails($withdrawal_method, $withdrawal_details);
    if (!$validation_result['success']) {
        echo json_encode($validation_result);
        return;
    }
    
    // Check withdrawal limits
    $limits = getWithdrawalLimitsForMethod($withdrawal_method);
    if ($amount < $limits['min_amount']) {
        echo json_encode([
            'success' => false, 
            'message' => "Minimum withdrawal amount is $" . number_format($limits['min_amount'], 2)
        ]);
        return;
    }
    
    if ($amount > $limits['max_amount']) {
        echo json_encode([
            'success' => false, 
            'message' => "Maximum withdrawal amount is $" . number_format($limits['max_amount'], 2)
        ]);
        return;
    }
    
    global $link;
    
    // Check user's wallet balance
    $wallet = $credit_system->getUserWallet($user_id);
    if (!$wallet || $wallet['balance'] < $amount) {
        echo json_encode(['success' => false, 'message' => 'Insufficient balance']);
        return;
    }
    
    // Check daily withdrawal limit
    $daily_limit_check = checkDailyWithdrawalLimit($user_id, $amount);
    if (!$daily_limit_check['success']) {
        echo json_encode($daily_limit_check);
        return;
    }
    
    // Generate unique withdrawal request ID
    $withdrawal_id = 'WD' . date('Ymd') . strtoupper(substr(uniqid(), -6));
    
    // Calculate fees
    $fee_info = calculateWithdrawalFees($amount, $withdrawal_method);
    $net_amount = $amount - $fee_info['total_fee'];
    
    mysqli_begin_transaction($link);
    
    try {
        // Create withdrawal request
        $sql = "INSERT INTO withdrawal_requests (user_id, withdrawal_id, amount, net_amount, withdrawal_method, 
                                               withdrawal_details, processing_fee, platform_fee, total_fee, 
                                               status, notes) 
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, 'pending', ?)";
        
        $details_json = json_encode($withdrawal_details);
        $stmt = execute_query($link, $sql, "isddssdddss", [
            $user_id, $withdrawal_id, $amount, $net_amount, $withdrawal_method, $details_json,
            $fee_info['processing_fee'], $fee_info['platform_fee'], $fee_info['total_fee'], $notes
        ]);
        
        if (!$stmt) {
            throw new Exception('Failed to create withdrawal request');
        }
        
        $request_id = mysqli_insert_id($link);
        mysqli_stmt_close($stmt);
        
        // Deduct amount from wallet (freeze it)
        $deduct_result = $credit_system->deductCredit(
            $user_id, 
            $amount, 
            "Withdrawal request: $withdrawal_id", 
            'withdrawal', 
            $request_id
        );
        
        if (!$deduct_result['success']) {
            throw new Exception($deduct_result['message']);
        }
        
        mysqli_commit($link);
        
        echo json_encode([
            'success' => true,
            'message' => 'Withdrawal request submitted successfully',
            'withdrawal_id' => $withdrawal_id,
            'request_id' => $request_id,
            'amount' => number_format($amount, 2),
            'net_amount' => number_format($net_amount, 2),
            'fee_info' => $fee_info,
            'processing_time' => $limits['processing_time']
        ]);
        
    } catch (Exception $e) {
        mysqli_rollback($link);
        echo json_encode(['success' => false, 'message' => $e->getMessage()]);
    }
}

/**
 * Get withdrawal history
 */
function getWithdrawalHistory($user_id) {
    global $link;
    
    $page = intval($_GET['page'] ?? 1);
    $limit = intval($_GET['limit'] ?? 20);
    $offset = ($page - 1) * $limit;
    
    $sql = "SELECT * FROM withdrawal_requests 
            WHERE user_id = ? 
            ORDER BY created_at DESC 
            LIMIT ? OFFSET ?";
    
    $stmt = execute_query($link, $sql, "iii", [$user_id, $limit, $offset]);
    
    if (!$stmt) {
        echo json_encode(['success' => false, 'message' => 'Database error']);
        return;
    }
    
    $result = mysqli_stmt_get_result($stmt);
    $withdrawals = [];
    
    while ($row = mysqli_fetch_assoc($result)) {
        $details = json_decode($row['withdrawal_details'], true);
        
        $withdrawals[] = [
            'id' => $row['id'],
            'withdrawal_id' => $row['withdrawal_id'],
            'amount' => number_format($row['amount'], 2),
            'net_amount' => number_format($row['net_amount'], 2),
            'withdrawal_method' => $row['withdrawal_method'],
            'withdrawal_details' => $details,
            'status' => $row['status'],
            'status_display' => getWithdrawalStatusDisplay($row['status']),
            'created_at' => date('Y-m-d H:i:s', strtotime($row['created_at'])),
            'processed_at' => $row['processed_at'] ? date('Y-m-d H:i:s', strtotime($row['processed_at'])) : null,
            'completed_at' => $row['completed_at'] ? date('Y-m-d H:i:s', strtotime($row['completed_at'])) : null,
            'admin_notes' => $row['admin_notes'],
            'total_fee' => number_format($row['total_fee'], 2),
            'can_cancel' => $row['status'] === 'pending'
        ];
    }
    
    mysqli_stmt_close($stmt);
    
    echo json_encode([
        'success' => true,
        'withdrawals' => $withdrawals,
        'pagination' => [
            'page' => $page,
            'limit' => $limit,
            'has_more' => count($withdrawals) == $limit
        ]
    ]);
}

/**
 * Cancel pending withdrawal
 */
function cancelWithdrawal($user_id) {
    global $link;
    
    $withdrawal_id = trim($_POST['withdrawal_id'] ?? '');
    
    if (empty($withdrawal_id)) {
        echo json_encode(['success' => false, 'message' => 'Withdrawal ID is required']);
        return;
    }
    
    mysqli_begin_transaction($link);
    
    try {
        // Get withdrawal request
        $sql = "SELECT * FROM withdrawal_requests 
                WHERE withdrawal_id = ? AND user_id = ? AND status = 'pending'";
        $stmt = execute_query($link, $sql, "si", [$withdrawal_id, $user_id]);
        
        if (!$stmt) {
            throw new Exception('Database error');
        }
        
        $result = mysqli_stmt_get_result($stmt);
        $withdrawal = mysqli_fetch_assoc($result);
        mysqli_stmt_close($stmt);
        
        if (!$withdrawal) {
            throw new Exception('Withdrawal request not found or cannot be cancelled');
        }
        
        // Cancel the request
        $sql = "UPDATE withdrawal_requests 
                SET status = 'cancelled', updated_at = NOW() 
                WHERE withdrawal_id = ? AND user_id = ?";
        $stmt = execute_query($link, $sql, "si", [$withdrawal_id, $user_id]);
        
        if (!$stmt) {
            throw new Exception('Failed to cancel withdrawal request');
        }
        mysqli_stmt_close($stmt);
        
        // Restore balance to wallet
        global $credit_system;
        $restore_result = $credit_system->addCredit(
            $user_id,
            $withdrawal['amount'],
            'refund',
            "Cancelled withdrawal: $withdrawal_id",
            "CANCEL_$withdrawal_id"
        );
        
        if (!$restore_result['success']) {
            throw new Exception($restore_result['message']);
        }
        
        mysqli_commit($link);
        
        echo json_encode([
            'success' => true,
            'message' => 'Withdrawal request cancelled successfully',
            'refunded_amount' => number_format($withdrawal['amount'], 2)
        ]);
        
    } catch (Exception $e) {
        mysqli_rollback($link);
        echo json_encode(['success' => false, 'message' => $e->getMessage()]);
    }
}

/**
 * Get withdrawal limits for user
 */
function getWithdrawalLimits($user_id) {
    global $link;
    
    // Get user's current wallet balance
    global $credit_system;
    $wallet = $credit_system->getUserWallet($user_id);
    $available_balance = $wallet ? $wallet['balance'] : 0;
    
    // Get daily withdrawal limit
    $daily_limit = getDailyWithdrawalLimit($user_id);
    
    // Get today's withdrawals
    $today_withdrawals = getTodayWithdrawals($user_id);
    $remaining_daily_limit = max(0, $daily_limit - $today_withdrawals);
    
    echo json_encode([
        'success' => true,
        'limits' => [
            'available_balance' => number_format($available_balance, 2),
            'daily_limit' => number_format($daily_limit, 2),
            'today_withdrawals' => number_format($today_withdrawals, 2),
            'remaining_daily_limit' => number_format($remaining_daily_limit, 2),
            'max_withdrawal_amount' => number_format(min($available_balance, $remaining_daily_limit), 2)
        ]
    ]);
}

/**
 * Validate withdrawal details based on method
 */
function validateWithdrawalDetails($method, $details) {
    $required_fields = [];
    
    switch ($method) {
        case 'paypal':
            $required_fields = ['paypal_email'];
            break;
        case 'bank_transfer':
            $required_fields = ['account_number', 'routing_number', 'account_holder_name', 'bank_name'];
            break;
        case 'crypto':
            $required_fields = ['crypto_type', 'wallet_address'];
            break;
        case 'check':
            $required_fields = ['full_name', 'address', 'city', 'state', 'zip_code'];
            break;
    }
    
    foreach ($required_fields as $field) {
        if (empty($details[$field])) {
            return [
                'success' => false,
                'message' => "Required field missing: " . str_replace('_', ' ', $field)
            ];
        }
    }
    
    // Additional validation
    if ($method === 'paypal' && !filter_var($details['paypal_email'], FILTER_VALIDATE_EMAIL)) {
        return ['success' => false, 'message' => 'Invalid PayPal email address'];
    }
    
    if ($method === 'crypto' && !in_array($details['crypto_type'], ['bitcoin', 'ethereum'])) {
        return ['success' => false, 'message' => 'Unsupported cryptocurrency type'];
    }
    
    return ['success' => true];
}

/**
 * Get withdrawal limits for specific method
 */
function getWithdrawalLimitsForMethod($method) {
    $limits = [
        'paypal' => ['min_amount' => 25.00, 'max_amount' => 2000.00, 'processing_time' => '1-3 business days'],
        'bank_transfer' => ['min_amount' => 50.00, 'max_amount' => 5000.00, 'processing_time' => '3-5 business days'],
        'crypto' => ['min_amount' => 100.00, 'max_amount' => 10000.00, 'processing_time' => '30 minutes - 2 hours'],
        'check' => ['min_amount' => 100.00, 'max_amount' => 3000.00, 'processing_time' => '7-10 business days']
    ];
    
    return $limits[$method] ?? ['min_amount' => 25.00, 'max_amount' => 1000.00, 'processing_time' => '1-3 business days'];
}

/**
 * Calculate withdrawal fees
 */
function calculateWithdrawalFees($amount, $method) {
    $fee_rates = [
        'paypal' => ['percentage' => 1.0, 'fixed' => 0.0],
        'bank_transfer' => ['percentage' => 0.0, 'fixed' => 2.50],
        'crypto' => ['percentage' => 2.0, 'fixed' => 0.0],
        'check' => ['percentage' => 0.0, 'fixed' => 5.00]
    ];
    
    $rates = $fee_rates[$method] ?? ['percentage' => 1.0, 'fixed' => 0.0];
    
    $processing_fee = ($amount * $rates['percentage'] / 100) + $rates['fixed'];
    $platform_fee = 0.0;
    $total_fee = $processing_fee + $platform_fee;
    
    return [
        'processing_fee' => $processing_fee,
        'platform_fee' => $platform_fee,
        'total_fee' => $total_fee,
        'processing_fee_display' => number_format($processing_fee, 2),
        'platform_fee_display' => number_format($platform_fee, 2),
        'total_fee_display' => number_format($total_fee, 2)
    ];
}

/**
 * Check daily withdrawal limit
 */
function checkDailyWithdrawalLimit($user_id, $amount) {
    $daily_limit = getDailyWithdrawalLimit($user_id);
    $today_withdrawals = getTodayWithdrawals($user_id);
    
    if ($today_withdrawals + $amount > $daily_limit) {
        return [
            'success' => false,
            'message' => "Daily withdrawal limit exceeded. Remaining: $" . number_format($daily_limit - $today_withdrawals, 2)
        ];
    }
    
    return ['success' => true];
}

/**
 * Get daily withdrawal limit for user
 */
function getDailyWithdrawalLimit($user_id) {
    // Default limit, can be customized per user
    return 5000.00;
}

/**
 * Get today's withdrawal amount
 */
function getTodayWithdrawals($user_id) {
    global $link;
    
    $sql = "SELECT COALESCE(SUM(amount), 0) as total 
            FROM withdrawal_requests 
            WHERE user_id = ? 
            AND DATE(created_at) = CURDATE() 
            AND status IN ('pending', 'processing', 'completed')";
    
    $stmt = execute_query($link, $sql, "i", [$user_id]);
    
    if ($stmt) {
        $result = mysqli_stmt_get_result($stmt);
        $row = mysqli_fetch_assoc($result);
        mysqli_stmt_close($stmt);
        return $row['total'];
    }
    
    return 0;
}

/**
 * Get display text for withdrawal status
 */
function getWithdrawalStatusDisplay($status) {
    $statuses = [
        'pending' => 'Pending Review',
        'processing' => 'Processing',
        'completed' => 'Completed',
        'failed' => 'Failed',
        'cancelled' => 'Cancelled',
        'rejected' => 'Rejected'
    ];
    
    return $statuses[$status] ?? ucfirst($status);
}
?>