<?php
require_once 'config.php';

header('Content-Type: application/json');

// --- Security Checks ---
if (!isset($_SESSION['user_id'])) {
    echo json_encode(['success' => false, 'message' => 'Access denied. You must be logged in.']);
    exit;
}
if (!isset($_POST['order_id']) || !isset($_POST['action'])) {
    echo json_encode(['success' => false, 'message' => 'Missing required parameters.']);
    exit;
}

$user_id = $_SESSION['user_id'];
$order_id = filter_var($_POST['order_id'], FILTER_VALIDATE_INT);
$action = $_POST['action'];

if ($order_id === false) {
    echo json_encode(['success' => false, 'message' => 'Invalid order ID.']);
    exit;
}

// --- Verify Ownership and Current Status ---
$sql_verify = "SELECT status FROM pc_orders WHERE id = ? AND user_id = ?";
$stmt_verify = execute_query($link, $sql_verify, "ii", [$order_id, $user_id]);
$result = mysqli_stmt_get_result($stmt_verify);
if (mysqli_num_rows($result) === 0) {
    echo json_encode(['success' => false, 'message' => 'Order not found or you do not have permission to modify it.']);
    mysqli_stmt_close($stmt_verify);
    exit;
}
$current_status = mysqli_fetch_assoc($result)['status'];
mysqli_stmt_close($stmt_verify);


// --- Determine New Status based on Action ---
$new_status = null;
$valid_action = false;

switch ($action) {
    case 'confirm_quote':
        if ($current_status === 'Quote Provided') {
            $new_status = 'Order Pending';
            $valid_action = true;
        }
        break;
    
    case 'cancel_order':
    case 'cancel_order':
        // Allow cancellation (deletion) only before payment
        if (in_array($current_status, ['Quote Provided', 'Order Pending'])) {
            $sql_delete = "DELETE FROM pc_orders WHERE id = ? AND user_id = ?";
            $stmt_delete = execute_query($link, $sql_delete, "ii", [$order_id, $user_id]);
            if ($stmt_delete && mysqli_stmt_affected_rows($stmt_delete) > 0) {
                echo json_encode(['success' => true, 'message' => 'Order has been successfully cancelled and removed.']);
            } else {
                echo json_encode(['success' => false, 'message' => 'Failed to cancel the order.']);
            }
            mysqli_stmt_close($stmt_delete);
            close_db_connection($link);
            exit; // Stop execution since we are done
        }
        $valid_action = false; // If it's a paid order, this will trigger the error below
        break;

    case 'make_payment': // This is a simulation
        if ($current_status === 'Order Pending') {
            $new_status = 'Payment Received';
            $valid_action = true;
        }
        break;

    case 'confirm_delivery':
        if ($current_status === 'Order Shipped') {
            $new_status = 'Order Delivered';
            $valid_action = true;
        }
        break;
}

if (!$valid_action) {
    echo json_encode(['success' => false, 'message' => "The action '{$action}' is not valid for the order's current status ('{$current_status}')."]);
    exit;
}

// --- Update Database ---
if ($new_status) {
    $sql_update = "UPDATE pc_orders SET status = ? WHERE id = ? AND user_id = ?";
    $stmt_update = execute_query($link, $sql_update, "sii", [$new_status, $order_id, $user_id]);

    if ($stmt_update && mysqli_stmt_affected_rows($stmt_update) > 0) {
        echo json_encode(['success' => true, 'message' => 'Order status updated successfully.']);
    } else {
        echo json_encode(['success' => false, 'message' => 'Failed to update order status.']);
    }

    mysqli_stmt_close($stmt_update);
}

close_db_connection($link);

?>