<?php
session_start();
require_once 'config.php';

header('Content-Type: application/json');

// Check if user is logged in
if (!isset($_SESSION["loggedin"]) || $_SESSION["loggedin"] !== true) {
    echo json_encode(['success' => false, 'message' => 'You must be logged in to submit an order.']);
    exit;
}

// Check if order details are provided
if (!isset($_POST['order_details']) || empty(trim($_POST['order_details']))) {
    echo json_encode(['success' => false, 'message' => 'Please provide details for your PC order.']);
    exit;
}

$user_id = $_SESSION['id'];
$order_details = trim($_POST['order_details']);

// Insert the new order into the database
$sql = "INSERT INTO pc_orders (user_id, order_details, status) VALUES (?, ?, 'Quote Requested')";

$stmt = execute_query($link, $sql, "is", [$user_id, $order_details]);

if ($stmt) {
    echo json_encode(['success' => true, 'message' => 'Order submitted successfully.']);
    mysqli_stmt_close($stmt);
} else {
    echo json_encode(['success' => false, 'message' => 'Failed to submit order. Please try again later.']);
}

close_db_connection($link);

?>