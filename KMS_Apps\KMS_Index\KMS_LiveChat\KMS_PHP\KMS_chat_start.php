<?php
header('Content-Type: application/json');
require_once __DIR__ . '/config.php';

$response = ['success' => false, 'message' => 'An unknown error occurred.'];
$link = get_db_connection();

try {
    $nickname = null;
    $email = null;
    $user_id = null;

    // Check if a logged-in user is starting the chat
    if (isset($_SESSION['loggedin']) && $_SESSION['loggedin'] === true) {
        $user_id = $_SESSION['id']; // Use 'id' to match the login script
        $nickname = $_SESSION['username'];
    } else {
        // Guest user, get data from POST
        $data = json_decode(file_get_contents('php://input'), true);
        $nickname = $data['nickname'] ?? null;
        $email = $data['email'] ?? null;

        if (empty($nickname) || empty($email)) {
            throw new Exception('Nickname and E-mail are required for guests.');
        }
        if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
            throw new Exception('Invalid E-mail format.');
        }
    }

    // Generate a unique session GUID
    $session_guid = bin2hex(random_bytes(16));

    $sql = "INSERT INTO chat_sessions (session_guid, user_id, guest_nickname, guest_email, status) VALUES (?, ?, ?, ?, 'pending')";
    $stmt = mysqli_prepare($link, $sql);
    
    if (!$stmt) {
        throw new Exception('Failed to prepare statement: ' . mysqli_error($link));
    }

    mysqli_stmt_bind_param($stmt, "sis_s", $session_guid, $user_id, $nickname, $email);
    
    if (mysqli_stmt_execute($stmt)) {
        $response['success'] = true;
        $response['message'] = 'Chat session started.';
        $response['session_guid'] = $session_guid;
        $response['chat_session_id'] = mysqli_insert_id($link);
    } else {
        throw new Exception('Failed to create chat session.');
    }

    mysqli_stmt_close($stmt);

} catch (Exception $e) {
    $response['message'] = $e->getMessage();
    error_log("Chat Start Error: " . $e->getMessage());
} finally {
    if ($link) {
        close_db_connection($link);
    }
}

echo json_encode($response);
?>