<?php
/**
 * Affiliate Tracker
 * Handles affiliate link tracking and referral processing
 * This file should be included in the main index.php or any landing page
 */

$base_path = dirname(dirname(dirname(dirname(dirname(__DIR__)))));
require_once $base_path . '/KMS_Apps/KMS_Core/KMS_Config/KMS_PHP/KMS_config.php';
require_once $base_path . '/KMS_Apps/KMS_Member/KMS_Affiliate/KMS_PHP/KMS_affiliate_system.php';

// Check if there's a referral code in the URL
if (isset($_GET['ref']) && !empty($_GET['ref'])) {
    $affiliate_code = trim($_GET['ref']);
    
    // Validate affiliate code format
    if (preg_match('/^KMS\d{4}[A-Z0-9]{4}$/', $affiliate_code)) {
        // Track the affiliate visit
        $referrer_url = $_SERVER['HTTP_REFERER'] ?? null;
        $landing_page = $_SERVER['REQUEST_URI'] ?? '/';
        
        $tracked = $affiliate_system->trackAffiliateVisit($affiliate_code, $referrer_url, $landing_page);
        
        if ($tracked) {
            // Redirect to clean URL (remove ref parameter)
            $clean_url = strtok($_SERVER['REQUEST_URI'], '?');
            if ($clean_url !== $_SERVER['REQUEST_URI']) {
                header("Location: $clean_url", true, 302);
                exit;
            }
        }
    }
}

/**
 * Function to be called when a new user registers
 * This should be called from the registration process
 */
function processAffiliateRegistration($new_user_id) {
    global $affiliate_system;
    
    // Create referral relationship if there's a tracked affiliate
    $affiliate_system->createReferral($new_user_id);
}

/**
 * Function to be called when an order is completed
 * This should be called from the order completion process
 */
function processAffiliateOrderCommission($order_id, $order_type = 'regular') {
    global $affiliate_system;
    
    // Process commission for the completed order
    $result = $affiliate_system->processOrderCommission($order_id, $order_type);
    
    if ($result && $result['success']) {
        // Log the commission payment
        error_log("Affiliate commission paid: $" . $result['amount'] . " to user " . $result['referrer_id'] . " for order " . $order_id);
        
        // You could also send notification email here
        // sendCommissionNotification($result['referrer_id'], $result['amount'], $order_id);
    }
    
    return $result;
}

/**
 * Generate affiliate sharing links for social media
 */
function generateSharingLinks($affiliate_code) {
    $base_url = 'https://' . $_SERVER['HTTP_HOST'];
    $referral_url = $base_url . '/?ref=' . $affiliate_code;
    $encoded_url = urlencode($referral_url);
    $message = urlencode("Check out KelvinKMS.com for amazing tech services!");
    
    return [
        'referral_url' => $referral_url,
        'facebook' => "https://www.facebook.com/sharer/sharer.php?u=$encoded_url",
        'twitter' => "https://twitter.com/intent/tweet?url=$encoded_url&text=$message",
        'linkedin' => "https://www.linkedin.com/sharing/share-offsite/?url=$encoded_url",
        'whatsapp' => "https://wa.me/?text=$message%20$encoded_url",
        'telegram' => "https://t.me/share/url?url=$encoded_url&text=$message",
        'email' => "mailto:?subject=Check%20out%20KelvinKMS.com&body=$message%20$encoded_url"
    ];
}

/**
 * Get affiliate performance analytics
 */
function getAffiliateAnalytics($user_id, $days = 30) {
    global $link;
    
    $analytics = [];
    
    // Get daily clicks for the past X days
    $sql = "SELECT DATE(created_at) as date, COUNT(*) as clicks
            FROM affiliate_tracking at
            JOIN affiliate_codes ac ON at.affiliate_code = ac.affiliate_code
            WHERE ac.user_id = ? AND at.created_at >= DATE_SUB(NOW(), INTERVAL ? DAY)
            GROUP BY DATE(created_at)
            ORDER BY date ASC";
    
    $stmt = execute_query($link, $sql, "ii", [$user_id, $days]);
    
    if ($stmt) {
        $result = mysqli_stmt_get_result($stmt);
        $clicks_data = [];
        
        while ($row = mysqli_fetch_assoc($result)) {
            $clicks_data[] = [
                'date' => $row['date'],
                'clicks' => (int)$row['clicks']
            ];
        }
        
        mysqli_stmt_close($stmt);
        $analytics['daily_clicks'] = $clicks_data;
    }
    
    // Get conversion data
    $sql = "SELECT DATE(ar.created_at) as date, COUNT(*) as conversions
            FROM affiliate_referrals ar
            WHERE ar.referrer_id = ? AND ar.created_at >= DATE_SUB(NOW(), INTERVAL ? DAY)
            GROUP BY DATE(ar.created_at)
            ORDER BY date ASC";
    
    $stmt = execute_query($link, $sql, "ii", [$user_id, $days]);
    
    if ($stmt) {
        $result = mysqli_stmt_get_result($stmt);
        $conversions_data = [];
        
        while ($row = mysqli_fetch_assoc($result)) {
            $conversions_data[] = [
                'date' => $row['date'],
                'conversions' => (int)$row['conversions']
            ];
        }
        
        mysqli_stmt_close($stmt);
        $analytics['daily_conversions'] = $conversions_data;
    }
    
    // Get commission data
    $sql = "SELECT DATE(paid_at) as date, SUM(commission_amount) as commissions
            FROM affiliate_commissions
            WHERE referrer_id = ? AND paid_at >= DATE_SUB(NOW(), INTERVAL ? DAY) AND status = 'paid'
            GROUP BY DATE(paid_at)
            ORDER BY date ASC";
    
    $stmt = execute_query($link, $sql, "ii", [$user_id, $days]);
    
    if ($stmt) {
        $result = mysqli_stmt_get_result($stmt);
        $commissions_data = [];
        
        while ($row = mysqli_fetch_assoc($result)) {
            $commissions_data[] = [
                'date' => $row['date'],
                'commissions' => (float)$row['commissions']
            ];
        }
        
        mysqli_stmt_close($stmt);
        $analytics['daily_commissions'] = $commissions_data;
    }
    
    // Get top referral sources
    $sql = "SELECT 
                CASE 
                    WHEN referrer_url IS NULL OR referrer_url = '' THEN 'Direct'
                    WHEN referrer_url LIKE '%facebook%' THEN 'Facebook'
                    WHEN referrer_url LIKE '%twitter%' THEN 'Twitter'
                    WHEN referrer_url LIKE '%linkedin%' THEN 'LinkedIn'
                    WHEN referrer_url LIKE '%whatsapp%' THEN 'WhatsApp'
                    WHEN referrer_url LIKE '%telegram%' THEN 'Telegram'
                    WHEN referrer_url LIKE '%google%' THEN 'Google'
                    ELSE 'Other'
                END as source,
                COUNT(*) as clicks
            FROM affiliate_tracking at
            JOIN affiliate_codes ac ON at.affiliate_code = ac.affiliate_code
            WHERE ac.user_id = ? AND at.created_at >= DATE_SUB(NOW(), INTERVAL ? DAY)
            GROUP BY source
            ORDER BY clicks DESC
            LIMIT 10";
    
    $stmt = execute_query($link, $sql, "ii", [$user_id, $days]);
    
    if ($stmt) {
        $result = mysqli_stmt_get_result($stmt);
        $sources_data = [];
        
        while ($row = mysqli_fetch_assoc($result)) {
            $sources_data[] = [
                'source' => $row['source'],
                'clicks' => (int)$row['clicks']
            ];
        }
        
        mysqli_stmt_close($stmt);
        $analytics['top_sources'] = $sources_data;
    }
    
    return $analytics;
}

/**
 * Send commission notification email (optional)
 */
function sendCommissionNotification($user_id, $amount, $order_id) {
    global $link;
    
    // Get user email
    $sql = "SELECT email, first_name, last_name FROM users WHERE id = ?";
    $stmt = execute_query($link, $sql, "i", [$user_id]);
    
    if ($stmt) {
        $result = mysqli_stmt_get_result($stmt);
        $user = mysqli_fetch_assoc($result);
        mysqli_stmt_close($stmt);
        
        if ($user && $user['email']) {
            $to = $user['email'];
            $name = trim($user['first_name'] . ' ' . $user['last_name']) ?: 'Affiliate Partner';
            $subject = 'Commission Earned - KelvinKMS.com';
            
            $message = "
            <html>
            <head>
                <title>Commission Earned</title>
            </head>
            <body>
                <h2>Congratulations! You've earned a commission!</h2>
                <p>Dear $name,</p>
                <p>Great news! You've earned a commission of <strong>$$amount</strong> from a successful referral.</p>
                <p><strong>Details:</strong></p>
                <ul>
                    <li>Commission Amount: $$amount</li>
                    <li>Order ID: #$order_id</li>
                    <li>Date: " . date('Y-m-d H:i:s') . "</li>
                </ul>
                <p>The commission has been added to your KMS Credit balance and is available for withdrawal.</p>
                <p>Thank you for being a valued affiliate partner!</p>
                <p>Best regards,<br>The KelvinKMS.com Team</p>
            </body>
            </html>
            ";
            
            $headers = "MIME-Version: 1.0" . "\r\n";
            $headers .= "Content-type:text/html;charset=UTF-8" . "\r\n";
            $headers .= "From: <EMAIL>" . "\r\n";
            
            mail($to, $subject, $message, $headers);
        }
    }
}
?>
