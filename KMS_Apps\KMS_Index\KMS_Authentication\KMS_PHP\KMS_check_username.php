<?php
header('Content-Type: application/json');
require_once 'config.php';
require_once 'functions.php';

if ($_SERVER["REQUEST_METHOD"] == "POST") {
    $username = sanitize_input($_POST['username']);
    
    if (empty($username)) {
        echo json_encode([
            'success' => false,
            'message' => 'Username is required.'
        ]);
        exit;
    }
    
    // Check if username already exists
    $check_sql = "SELECT id FROM users WHERE username = ?";
    $check_stmt = execute_query($link, $check_sql, "s", [$username]);
    
    if ($check_stmt) {
        $result = mysqli_stmt_get_result($check_stmt);
        $exists = mysqli_fetch_assoc($result) ? true : false;
        mysqli_stmt_close($check_stmt);
        
        echo json_encode([
            'success' => true,
            'exists' => $exists
        ]);
    } else {
        echo json_encode([
            'success' => false,
            'message' => 'Database error occurred.'
        ]);
    }
    
    close_db_connection($link);
} else {
    echo json_encode([
        'success' => false,
        'message' => 'Invalid request method.'
    ]);
}
?>
