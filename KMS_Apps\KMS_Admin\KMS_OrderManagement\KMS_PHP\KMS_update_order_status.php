<?php
session_start();
header('Content-Type: application/json');
require_once 'config.php';
require_once 'functions.php';

// Check if user is logged in and is admin
if (!isset($_SESSION["loggedin"]) || $_SESSION["loggedin"] !== true || !isset($_SESSION["is_admin"]) || $_SESSION["is_admin"] !== true) {
    echo json_encode([
        'success' => false,
        'message' => 'Access denied. Admin privileges required.'
    ]);
    exit;
}

if ($_SERVER["REQUEST_METHOD"] == "POST") {
    $order_id = (int)$_POST['order_id'];
    $status = sanitize_input($_POST['status']);
    
    // Validate status
    $valid_statuses = ['pending', 'processing', 'completed', 'cancelled'];
    if (!in_array($status, $valid_statuses)) {
        echo json_encode([
            'success' => false,
            'message' => 'Invalid status value.'
        ]);
        exit;
    }
    
    // Update order status
    $sql = "UPDATE orders SET status = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?";
    $stmt = execute_query($link, $sql, "si", [$status, $order_id]);
    
    if ($stmt) {
        $affected_rows = mysqli_stmt_affected_rows($stmt);
        mysqli_stmt_close($stmt);
        
        if ($affected_rows > 0) {
            echo json_encode([
                'success' => true,
                'message' => 'Order status updated successfully!'
            ]);
        } else {
            echo json_encode([
                'success' => false,
                'message' => 'Order not found or no changes made.'
            ]);
        }
    } else {
        echo json_encode([
            'success' => false,
            'message' => 'Failed to update order status.'
        ]);
    }
    
    close_db_connection($link);
} else {
    echo json_encode([
        'success' => false,
        'message' => 'Invalid request method.'
    ]);
}
?>
