<?php
/**
 * Admin Withdrawal Management API
 * Handles withdrawal request processing for administrators
 */

require_once 'config.php';
require_once 'credit_system.php';

// Check if user is admin
if (!isset($_SESSION['user_id']) || $_SESSION['username'] !== 'admin') {
    echo json_encode(['success' => false, 'message' => 'Admin access required']);
    exit;
}

$admin_user_id = $_SESSION['user_id'];
$action = $_POST['action'] ?? $_GET['action'] ?? '';

header('Content-Type: application/json');

switch ($action) {
    case 'get_pending_withdrawals':
        getPendingWithdrawals();
        break;
        
    case 'get_all_withdrawals':
        getAllWithdrawals();
        break;
        
    case 'approve_withdrawal':
        approveWithdrawal($admin_user_id);
        break;
        
    case 'reject_withdrawal':
        rejectWithdrawal($admin_user_id);
        break;
        
    case 'complete_withdrawal':
        completeWithdrawal($admin_user_id);
        break;
        
    case 'get_withdrawal_stats':
        getWithdrawalStats();
        break;
        
    default:
        echo json_encode(['success' => false, 'message' => 'Invalid action']);
        break;
}

/**
 * Get pending withdrawal requests
 */
function getPendingWithdrawals() {
    global $link;
    
    $sql = "SELECT wr.*, u.username, u.email, uw.commission_balance
            FROM withdrawal_requests wr
            JOIN users u ON wr.user_id = u.id
            LEFT JOIN user_wallets uw ON wr.user_id = uw.user_id
            WHERE wr.status IN ('pending', 'processing')
            ORDER BY wr.requested_at ASC";
    
    $result = mysqli_query($link, $sql);
    
    if (!$result) {
        echo json_encode(['success' => false, 'message' => 'Database error']);
        return;
    }
    
    $withdrawals = [];
    while ($row = mysqli_fetch_assoc($result)) {
        $withdrawal_details = null;
        if ($row['withdrawal_details']) {
            $withdrawal_details = json_decode($row['withdrawal_details'], true);
        }
        
        $withdrawals[] = [
            'id' => $row['id'],
            'request_id' => $row['request_id'],
            'username' => $row['username'],
            'email' => $row['email'],
            'amount' => number_format($row['amount'], 2),
            'method' => $row['withdrawal_method'],
            'details' => $withdrawal_details,
            'status' => $row['status'],
            'commission_balance' => number_format($row['commission_balance'] ?? 0, 2),
            'requested_at' => date('Y-m-d H:i:s', strtotime($row['requested_at'])),
            'admin_notes' => $row['admin_notes']
        ];
    }
    
    echo json_encode([
        'success' => true,
        'withdrawals' => $withdrawals
    ]);
}

/**
 * Get all withdrawal requests with pagination
 */
function getAllWithdrawals() {
    global $link;
    
    $page = intval($_GET['page'] ?? 1);
    $limit = intval($_GET['limit'] ?? 50);
    $offset = ($page - 1) * $limit;
    $status_filter = trim($_GET['status'] ?? '');
    
    $where_conditions = [];
    $params = [];
    $types = '';
    
    if (!empty($status_filter)) {
        $where_conditions[] = "wr.status = ?";
        $params[] = $status_filter;
        $types .= 's';
    }
    
    $where_clause = '';
    if (!empty($where_conditions)) {
        $where_clause = 'WHERE ' . implode(' AND ', $where_conditions);
    }
    
    $sql = "SELECT wr.*, u.username, u.email, admin.username as processed_by_username
            FROM withdrawal_requests wr
            JOIN users u ON wr.user_id = u.id
            LEFT JOIN users admin ON wr.processed_by = admin.id
            $where_clause
            ORDER BY wr.requested_at DESC
            LIMIT ? OFFSET ?";
    
    $params[] = $limit;
    $params[] = $offset;
    $types .= 'ii';
    
    $stmt = execute_query($link, $sql, $types, $params);
    
    if (!$stmt) {
        echo json_encode(['success' => false, 'message' => 'Database error']);
        return;
    }
    
    $result = mysqli_stmt_get_result($stmt);
    $withdrawals = [];
    
    while ($row = mysqli_fetch_assoc($result)) {
        $withdrawals[] = [
            'id' => $row['id'],
            'request_id' => $row['request_id'],
            'username' => $row['username'],
            'email' => $row['email'],
            'amount' => number_format($row['amount'], 2),
            'method' => $row['withdrawal_method'],
            'status' => $row['status'],
            'requested_at' => date('Y-m-d H:i:s', strtotime($row['requested_at'])),
            'processed_at' => $row['processed_at'] ? date('Y-m-d H:i:s', strtotime($row['processed_at'])) : null,
            'completed_at' => $row['completed_at'] ? date('Y-m-d H:i:s', strtotime($row['completed_at'])) : null,
            'processed_by' => $row['processed_by_username'],
            'admin_notes' => $row['admin_notes']
        ];
    }
    
    mysqli_stmt_close($stmt);
    
    echo json_encode([
        'success' => true,
        'withdrawals' => $withdrawals,
        'pagination' => [
            'page' => $page,
            'limit' => $limit,
            'has_more' => count($withdrawals) == $limit
        ]
    ]);
}

/**
 * Approve withdrawal request
 */
function approveWithdrawal($admin_user_id) {
    global $link;
    
    $request_id = trim($_POST['request_id'] ?? '');
    $admin_notes = trim($_POST['admin_notes'] ?? '');
    
    if (empty($request_id)) {
        echo json_encode(['success' => false, 'message' => 'Request ID is required']);
        return;
    }
    
    mysqli_begin_transaction($link);
    
    try {
        // Get withdrawal request details
        $sql = "SELECT * FROM withdrawal_requests WHERE request_id = ? AND status = 'pending'";
        $stmt = execute_query($link, $sql, "s", [$request_id]);
        
        if (!$stmt) {
            throw new Exception('Database error');
        }
        
        $result = mysqli_stmt_get_result($stmt);
        $withdrawal = mysqli_fetch_assoc($result);
        mysqli_stmt_close($stmt);
        
        if (!$withdrawal) {
            throw new Exception('Withdrawal request not found or already processed');
        }
        
        // Update withdrawal status to approved
        $update_sql = "UPDATE withdrawal_requests SET status = 'approved', admin_notes = ?, processed_by = ?, processed_at = NOW() WHERE request_id = ?";
        $update_stmt = execute_query($link, $update_sql, "sis", [$admin_notes, $admin_user_id, $request_id]);
        
        if (!$update_stmt) {
            throw new Exception('Failed to update withdrawal status');
        }
        mysqli_stmt_close($update_stmt);
        
        mysqli_commit($link);
        
        echo json_encode([
            'success' => true,
            'message' => 'Withdrawal request approved successfully'
        ]);
        
    } catch (Exception $e) {
        mysqli_rollback($link);
        echo json_encode(['success' => false, 'message' => $e->getMessage()]);
    }
}

/**
 * Reject withdrawal request
 */
function rejectWithdrawal($admin_user_id) {
    global $link;
    
    $request_id = trim($_POST['request_id'] ?? '');
    $admin_notes = trim($_POST['admin_notes'] ?? '');
    
    if (empty($request_id)) {
        echo json_encode(['success' => false, 'message' => 'Request ID is required']);
        return;
    }
    
    if (empty($admin_notes)) {
        echo json_encode(['success' => false, 'message' => 'Rejection reason is required']);
        return;
    }
    
    mysqli_begin_transaction($link);
    
    try {
        // Get withdrawal request details
        $sql = "SELECT * FROM withdrawal_requests WHERE request_id = ? AND status IN ('pending', 'processing')";
        $stmt = execute_query($link, $sql, "s", [$request_id]);
        
        if (!$stmt) {
            throw new Exception('Database error');
        }
        
        $result = mysqli_stmt_get_result($stmt);
        $withdrawal = mysqli_fetch_assoc($result);
        mysqli_stmt_close($stmt);
        
        if (!$withdrawal) {
            throw new Exception('Withdrawal request not found or already processed');
        }
        
        // Update withdrawal status to rejected
        $update_sql = "UPDATE withdrawal_requests SET status = 'rejected', admin_notes = ?, processed_by = ?, processed_at = NOW() WHERE request_id = ?";
        $update_stmt = execute_query($link, $update_sql, "sis", [$admin_notes, $admin_user_id, $request_id]);
        
        if (!$update_stmt) {
            throw new Exception('Failed to update withdrawal status');
        }
        mysqli_stmt_close($update_stmt);
        
        // Restore commission balance to user
        $restore_sql = "UPDATE user_wallets SET commission_balance = commission_balance + ? WHERE user_id = ?";
        $restore_stmt = execute_query($link, $restore_sql, "di", [$withdrawal['amount'], $withdrawal['user_id']]);
        
        if (!$restore_stmt) {
            throw new Exception('Failed to restore commission balance');
        }
        mysqli_stmt_close($restore_stmt);
        
        mysqli_commit($link);
        
        echo json_encode([
            'success' => true,
            'message' => 'Withdrawal request rejected and balance restored'
        ]);
        
    } catch (Exception $e) {
        mysqli_rollback($link);
        echo json_encode(['success' => false, 'message' => $e->getMessage()]);
    }
}

/**
 * Mark withdrawal as completed
 */
function completeWithdrawal($admin_user_id) {
    global $link;
    
    $request_id = trim($_POST['request_id'] ?? '');
    $admin_notes = trim($_POST['admin_notes'] ?? '');
    
    if (empty($request_id)) {
        echo json_encode(['success' => false, 'message' => 'Request ID is required']);
        return;
    }
    
    mysqli_begin_transaction($link);
    
    try {
        // Update withdrawal status to completed
        $update_sql = "UPDATE withdrawal_requests SET status = 'completed', admin_notes = ?, completed_at = NOW() WHERE request_id = ? AND status = 'approved'";
        $update_stmt = execute_query($link, $update_sql, "ss", [$admin_notes, $request_id]);
        
        if (!$update_stmt || mysqli_stmt_affected_rows($update_stmt) == 0) {
            throw new Exception('Withdrawal request not found or not in approved status');
        }
        mysqli_stmt_close($update_stmt);
        
        // Update affiliate code total withdrawn
        $sql = "SELECT wr.user_id, wr.amount FROM withdrawal_requests WHERE request_id = ?";
        $stmt = execute_query($link, $sql, "s", [$request_id]);
        
        if ($stmt) {
            $result = mysqli_stmt_get_result($stmt);
            $withdrawal = mysqli_fetch_assoc($result);
            mysqli_stmt_close($stmt);
            
            if ($withdrawal) {
                $update_affiliate_sql = "UPDATE affiliate_codes SET total_withdrawn = total_withdrawn + ? WHERE user_id = ?";
                $update_affiliate_stmt = execute_query($link, $update_affiliate_sql, "di", [$withdrawal['amount'], $withdrawal['user_id']]);
                if ($update_affiliate_stmt) mysqli_stmt_close($update_affiliate_stmt);
            }
        }
        
        mysqli_commit($link);
        
        echo json_encode([
            'success' => true,
            'message' => 'Withdrawal marked as completed successfully'
        ]);
        
    } catch (Exception $e) {
        mysqli_rollback($link);
        echo json_encode(['success' => false, 'message' => $e->getMessage()]);
    }
}

/**
 * Get withdrawal statistics
 */
function getWithdrawalStats() {
    global $link;
    
    // Total pending withdrawals
    $pending_sql = "SELECT COUNT(*) as count, SUM(amount) as total FROM withdrawal_requests WHERE status = 'pending'";
    $pending_result = mysqli_query($link, $pending_sql);
    $pending_stats = mysqli_fetch_assoc($pending_result);
    
    // Total completed withdrawals
    $completed_sql = "SELECT COUNT(*) as count, SUM(amount) as total FROM withdrawal_requests WHERE status = 'completed'";
    $completed_result = mysqli_query($link, $completed_sql);
    $completed_stats = mysqli_fetch_assoc($completed_result);
    
    // Recent withdrawals (last 7 days)
    $recent_sql = "SELECT COUNT(*) as count FROM withdrawal_requests WHERE requested_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)";
    $recent_result = mysqli_query($link, $recent_sql);
    $recent_stats = mysqli_fetch_assoc($recent_result);
    
    echo json_encode([
        'success' => true,
        'stats' => [
            'pending_count' => $pending_stats['count'],
            'pending_amount' => number_format($pending_stats['total'] ?? 0, 2),
            'completed_count' => $completed_stats['count'],
            'completed_amount' => number_format($completed_stats['total'] ?? 0, 2),
            'recent_requests' => $recent_stats['count']
        ]
    ]);
}
?>
