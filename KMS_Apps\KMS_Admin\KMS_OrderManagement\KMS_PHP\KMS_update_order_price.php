<?php
session_start();
header('Content-Type: application/json');
require_once 'config.php';
require_once 'functions.php';

// Check if user is logged in and is admin
if (!isset($_SESSION["loggedin"]) || $_SESSION["loggedin"] !== true || !isset($_SESSION["is_admin"]) || $_SESSION["is_admin"] !== true) {
    echo json_encode([
        'success' => false,
        'message' => 'Access denied. Admin privileges required.'
    ]);
    exit;
}

if ($_SERVER["REQUEST_METHOD"] == "POST") {
    $order_id = (int)$_POST['order_id'];
    $price_type = sanitize_input($_POST['price_type']);
    $price = (float)$_POST['price'];
    
    // Validate price type
    if (!in_array($price_type, ['estimated', 'final'])) {
        echo json_encode([
            'success' => false,
            'message' => 'Invalid price type.'
        ]);
        exit;
    }
    
    // Validate price
    if ($price <= 0) {
        echo json_encode([
            'success' => false,
            'message' => 'Price must be greater than 0.'
        ]);
        exit;
    }
    
    // Update order price
    $column = $price_type . '_price';
    $sql = "UPDATE orders SET $column = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?";
    $stmt = execute_query($link, $sql, "di", [$price, $order_id]);
    
    if ($stmt) {
        $affected_rows = mysqli_stmt_affected_rows($stmt);
        mysqli_stmt_close($stmt);
        
        if ($affected_rows > 0) {
            echo json_encode([
                'success' => true,
                'message' => ucfirst($price_type) . ' price updated successfully!'
            ]);
        } else {
            echo json_encode([
                'success' => false,
                'message' => 'Order not found or no changes made.'
            ]);
        }
    } else {
        echo json_encode([
            'success' => false,
            'message' => 'Failed to update price.'
        ]);
    }
    
    close_db_connection($link);
} else {
    echo json_encode([
        'success' => false,
        'message' => 'Invalid request method.'
    ]);
}
?>
