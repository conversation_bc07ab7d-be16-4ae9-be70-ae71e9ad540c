<?php
// Simple session start with error reporting
ini_set('display_errors', 1);
error_reporting(E_ALL);

// Start session
session_start();

// Include required files
require_once 'config.php';
require_once 'language.php';

// Debug session data
error_log('Member Page - Simple session data: ' . print_r([
    'session_id' => session_id(),
    'session_name' => session_name(),
    'loggedin' => $_SESSION['loggedin'] ?? 'not set',
    'user_id' => $_SESSION['id'] ?? 'not set'
], true));

// Check if user is logged in
if (!isset($_SESSION['loggedin']) || $_SESSION['loggedin'] !== true) {
    error_log('Redirecting to login: User not logged in. Session data: ' . print_r($_SESSION, true));
    header('Location: ../index.php');
    exit;
}

// Store session ID for JavaScript
echo "<script>const SESSION_ID = '" . session_id() . "';</script>";
?>
<!DOCTYPE html>
<html lang="<?= $lang ?>">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= t('member_page_title') ?></title>
    <link rel="stylesheet" href="../CSS/custom-modal.css" />
    <link rel="stylesheet" href="../CSS/member.css" />
</head>
<body>
    <!-- Top right buttons -->
    <div class="top-buttons">
        <button class="account-settings-btn" id="accountSettingsBtn">
            ⚙️ Account Settings
        </button>
        <button class="logout-btn" id="logoutBtn">
            <?= t('logout_button') ?>
        </button>
    </div>

    <div class="container">
        <h1 id="welcome-msg"><?= str_replace('{username}', htmlspecialchars($_SESSION["username"]), t('welcome_message')) ?></h1>

        <!-- KMS Credit Wallet Section -->
        <div class="wallet-section">
            <h2>💰 KMS Credit Wallet</h2>
            <div class="wallet-card">
                <div class="wallet-balance">
                    <h3>Available Balance</h3>
                    <div class="balance-amount" id="walletBalance">$0.00</div>
                </div>

                <div class="wallet-stats">
                    <div class="stat-item">
                        <h4>Total Deposited</h4>
                        <div class="stat-value" id="totalDeposited">$0.00</div>
                    </div>
                    <div class="stat-item">
                        <h4>Total Spent</h4>
                        <div class="stat-value" id="totalSpent">$0.00</div>
                    </div>
                    <div class="stat-item">
                        <h4>Frozen Balance</h4>
                        <div class="stat-value" id="frozenBalance">$0.00</div>
                    </div>
                </div>

                <div class="wallet-actions">
                    <button class="deposit-btn" onclick="openDepositModal()">💳 Deposit</button>
                    <button class="transfer-btn" onclick="openTransferModal()">💸 Transfer</button>
                    <button class="history-btn" onclick="openTransactionHistory()">📊 History</button>
                </div>
            </div>
        </div>

        <!-- Affiliate Section -->
        <div class="wallet-section">
            <h2>🤝 Affiliate Program</h2>
            <div class="wallet-card">
                <div class="wallet-balance">
                    <h3>Your Referral Code</h3>
                    <div style="font-size: 24px; color: #00ff00; font-weight: bold; margin: 10px 0;" id="affiliateCode">Loading...</div>
                    <div style="font-size: 14px; color: #ccc;">Share this code to earn $50 per successful referral!</div>
                </div>

                <div class="wallet-stats">
                    <div class="stat-item">
                        <h4>Total Referrals</h4>
                        <div class="stat-value" id="totalReferrals">0</div>
                    </div>
                    <div class="stat-item">
                        <h4>Commission Earned</h4>
                        <div class="stat-value" id="totalCommissions">$0.00</div>
                    </div>
                    <div class="stat-item">
                        <h4>Available to Withdraw</h4>
                        <div class="stat-value" id="commissionBalance">$0.00</div>
                    </div>
                </div>

                <div class="wallet-actions">
                    <button class="btn-info" onclick="openShareModal()">📤 Share Link</button>
                    <button class="btn-primary" onclick="openTransferToKMSModal()">💳 Transfer to KMS Credit</button>
                    <button class="btn-success" onclick="openWithdrawModal()">💰 Withdraw</button>
                    <button class="history-btn" onclick="openAffiliateHistory()">📊 Referral History</button>
                </div>
            </div>
        </div>

        <!-- PC Builder Section -->
        <div class="wallet-section">
            <h2><?= t('pc_builder_title') ?></h2>
            <div class="wallet-card">
                <div class="pc-builder-container">
                    <!-- Mode Selection -->
                    <div class="mode-selection" style="margin-bottom: 30px;">
                        <h3 style="text-align: center; color: #00ffff; margin-bottom: 20px;"><?= t('pc_builder_mode_selection') ?></h3>
                        <div class="mode-options" style="display: flex; justify-content: center; gap: 15px; flex-wrap: wrap;">
                            <div class="mode-option" style="flex: 1; min-width: 150px; max-width: 200px; padding: 15px; background: rgba(0,0,0,0.3); border-radius: 10px; border: 2px solid transparent; cursor: pointer; text-align: center; transition: all 0.3s ease;" onclick="selectPCMode('simple')">
                                <h3 style="color: #00ff00; margin: 0; font-size: 20px; text-shadow: 1px 1px 2px rgba(0,0,0,0.8);">Simple Mode</h3>
                            </div>
                            <div class="mode-option" style="flex: 1; min-width: 150px; max-width: 200px; padding: 15px; background: rgba(0,0,0,0.3); border-radius: 10px; border: 2px solid transparent; cursor: pointer; text-align: center; transition: all 0.3s ease;" onclick="selectPCMode('detailed')">
                                <h3 style="color: #4ecdc4; margin: 0; font-size: 20px; text-shadow: 1px 1px 2px rgba(0,0,0,0.8);">Detailed Mode</h3>
                            </div>
                            <div class="mode-option" style="flex: 1; min-width: 150px; max-width: 200px; padding: 15px; background: rgba(0,0,0,0.3); border-radius: 10px; border: 2px solid transparent; cursor: pointer; text-align: center; transition: all 0.3s ease;" onclick="selectPCMode('prebuilt')">
                                <h3 style="color: #ffd700; margin: 0; font-size: 20px; text-shadow: 1px 1px 2px rgba(0,0,0,0.8);">Pre-built Packages</h3>
                            </div>
                        </div>

                        <!-- Admin Restriction Info -->
                        <div id="admin-restriction-info" style="margin-top: 20px; padding: 15px; background: rgba(255,165,0,0.1); border-radius: 10px; border-left: 4px solid #ffa500; display: none;">
                            <strong><?= t('pc_builder_admin_restriction') ?></strong>
                            <span id="restriction-text"><?= t('pc_builder_restriction_none') ?></span>
                        </div>
                    </div>

                    <!-- Configuration Areas -->
                    <div id="simple-mode-config" class="config-area" style="display: none;">
                        <h3 style="color: #00ff00; text-align: center; margin-bottom: 20px;"><?= t('pc_builder_simple_mode') ?></h3>
                        <div class="simple-config-grid" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px;">
                            <!-- Simple mode configuration will be loaded here -->
                        </div>
                    </div>

                    <div id="detailed-mode-config" class="config-area" style="display: none;">
                        <h3 style="color: #4ecdc4; text-align: center; margin-bottom: 20px;"><?= t('pc_builder_detailed_mode') ?></h3>
                        <div class="detailed-config-container">
                            <!-- Detailed component selection will be loaded here -->
                        </div>
                    </div>

                    <div id="prebuilt-mode-config" class="config-area" style="display: none;">
                        <h3 style="color: #ffd700; text-align: center; margin-bottom: 20px;"><?= t('pc_builder_prebuilt_mode') ?></h3>
                        <div class="prebuilt-configs-grid" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(350px, 1fr)); gap: 20px;">
                            <!-- Pre-built configurations will be loaded here -->
                        </div>
                    </div>

                    <!-- Configuration Summary -->
                    <div id="config-summary" style="display: none; margin-top: 30px; padding: 20px; background: rgba(0,255,255,0.1); border-radius: 10px; border: 2px solid #00ffff;">
                        <h3 style="color: #00ffff; text-align: center; margin-bottom: 20px;">Configuration Summary</h3>
                        <div id="summary-content"></div>
                        <div class="summary-total" style="text-align: center; margin-top: 20px;">
                            <h3 style="color: #ffd700;"><?= t('pc_builder_estimated_total') ?>: <span id="estimated-total">$0.00</span></h3>
                            <button class="btn-primary" onclick="requestPCQuote()" style="padding: 15px 30px; font-size: 18px; margin-top: 10px;"><?= t('pc_builder_request_quote') ?></button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Service Prices Section -->
        <div class="wallet-section">
            <h2>💰 Service Prices</h2>
            <div class="wallet-card">
                <div class="service-prices-container">
                    <div class="service-category">
                        <h3>📸 Optimize Photo & Video Services</h3>
                        <div id="optimizePrices" class="prices-list">
                            <div style="text-align: center; padding: 20px;">
                                <div style="font-size: 18px; color: #ccc;">Loading prices...</div>
                            </div>
                        </div>
                    </div>

                    <div class="service-category">
                        <h3>🖨️ Print Services</h3>
                        <div id="printPrices" class="prices-list">
                            <div style="text-align: center; padding: 20px;">
                                <div style="font-size: 18px; color: #ccc;">Loading prices...</div>
                            </div>
                        </div>
                    </div>

                    <div class="service-actions">
                        <button class="btn-primary" onclick="openOrderModal('optimize')">📸 Order Optimize Services</button>
                        <button class="btn-primary" onclick="openOrderModal('print')">🖨️ Order Print Services</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Order History Section -->
        <div class="wallet-section">
            <h2>📋 Order History</h2>
            <div class="wallet-card">
                <div id="ordersList" style="max-height: 400px; overflow-y: auto;">
                    <div style="text-align: center; padding: 20px;">
                        <div style="font-size: 18px; color: #ccc;">Loading orders...</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- External JavaScript Files -->
    <script src="../JS/member.js"></script>
    <script src="../JS/member-wallet.js"></script>
    <script src="../JS/member-pc-builder.js"></script>
    <script src="../JS/member-utils.js"></script>
    
    <script>
        // Initialize translations for JavaScript
        const i18n = <?= json_encode($translations, JSON_HEX_TAG | JSON_HEX_APOS | JSON_HEX_QUOT | JSON_HEX_AMP | JSON_UNESCAPED_UNICODE) ?>;
    </script>
</body>
</html>
