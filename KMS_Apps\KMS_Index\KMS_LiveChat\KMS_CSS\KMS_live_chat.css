/* Chat Widget Container */
.chat-widget-container {
    position: fixed;
    bottom: 30px;
    right: 25px;
    width: 300px; /* iPhone-like width */
    height: 600px; /* iPhone-like height */
    z-index: 1001;
    display: none; /* Hidden by default */
    transform: translateY(100%) scale(0.5);
    transform-origin: bottom right;
    transition: transform 0.3s ease-out, opacity 0.3s ease-out;
    opacity: 0;
}

.chat-widget-container.visible {
    display: block; /* Or flex, depending on children. Block is fine here. */
    transform: translateY(0) scale(1);
    opacity: 1;
}

/* The "Phone" Body */
.chat-widget-phone {
    width: 100%;
    height: 100%;
    background: #000000; /* Space Black-ish */
    border-radius: 35px; /* Rounded corners */
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5), inset 0 0 0 2px #444;
    padding: 6px;
    display: flex;
    flex-direction: column;
}

/* The "Screen" */
.chat-widget-screen {
    flex-grow: 1;
    background: #12d0df;
    border-radius: 28px; /* Inner screen radius */
    display: flex;
    flex-direction: column;
    overflow: hidden;
    position: relative;
    color: #ffffff;
}

/* Chat Header */
.chat-widget-header {
    background: #15d1b6;
    padding: 20px 15px 10px;
    border-bottom: 1px solid #e5e5e5;
    text-align: center;
    position: relative;
    padding-top: 40px; /* Make space for the notch */
}

.chat-widget-header h3 {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
}

.chat-widget-close-btn {
    position: absolute;
    top: 10px;
    right: 10px;
    background: #00ff73;
    color:#ffffff;
    border: none;
    width: 22px;
    height: 22px;
    border-radius: 50%;
    line-height: 18px;
    text-align: center;
    cursor: pointer;
    font-size: 18px;
}

/* Chat Body (both form and conversation) */
.chat-widget-body {
    flex-grow: 1;
    padding: 10px;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
}

/* Initial Guest Form */
#chatInitialForm p {
    font-size: 15px;
    color: #ffffff;
    text-align: center;
    margin-bottom: 10px;
}
#guestChatForm {
    display: flex;
    flex-direction: column;
    gap: 10px;
}
#guestChatForm input {
    padding: 10px;
    border: 1px solid #ccc;
    border-radius: 8px;
    font-size: 14px;
}
#guestChatForm input::placeholder {
    color: #999;
    opacity: 1;
}

#guestChatForm button {
    padding: 12px;
    border: none;
    border-radius: 12px;
    background-color: #007bff;
    color: white;
    font-size: 16px;
    cursor: pointer;
}

/* Conversation View */
.chat-messages {
    flex-grow: 1;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
    gap: 10px;
}
.chat-message {
    padding: 8px 12px;
    border-radius: 18px;
    max-width: 80%;
    word-wrap: break-word;
    font-size: 14px;
}
.chat-message.user {
    background-color: #007bff;
    color: white;
    align-self: flex-end;
    border-bottom-right-radius: 4px;
}
.chat-message.admin {
    background-color: #e5e5ea;
    color: #000;
    align-self: flex-start;
    border-bottom-left-radius: 4px;
}

/* Message Input Area */
.chat-input-area {
    padding-top: 10px;
    border-top: 1px solid #e5e5e5;
}
#chatMessageForm {
    display: flex;
    gap: 10px;
}
#chatMessageInput {
    flex-grow: 1;
    padding: 10px;
    border: 1px solid #ccc;
    border-radius: 20px;    
}
#chatMessageForm button {
    flex-shrink: 0;
    width: 40px;
    height: 40px;
    border: none;
    border-radius: 50%;
    background-color: #007bff;
    color: white;
    font-size: 18px;
    cursor: pointer;
    line-height: 40px;
}