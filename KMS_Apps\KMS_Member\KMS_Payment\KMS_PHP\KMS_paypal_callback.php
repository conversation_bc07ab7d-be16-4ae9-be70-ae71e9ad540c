<?php
/**
 * PayPal Payment Callback Handler
 * This handles PayPal payment notifications and updates deposit status
 */

require_once 'config.php';
require_once 'credit_system.php';

header('Content-Type: application/json');

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    echo json_encode(['success' => false, 'message' => 'User not logged in']);
    exit;
}

$action = $_POST['action'] ?? '';

switch ($action) {
    case 'simulate_payment_success':
        simulatePaymentSuccess();
        break;
        
    case 'handle_paypal_ipn':
        handlePayPalIPN();
        break;
        
    default:
        echo json_encode(['success' => false, 'message' => 'Invalid action']);
        break;
}

/**
 * Simulate successful payment (for demo purposes)
 */
function simulatePaymentSuccess() {
    global $credit_system, $link;
    
    $transaction_id = $_POST['transaction_id'] ?? '';
    $external_payment_id = $_POST['external_payment_id'] ?? '';
    
    if (empty($transaction_id)) {
        echo json_encode(['success' => false, 'message' => 'Transaction ID required']);
        return;
    }
    
    // Get deposit details
    $sql = "SELECT dr.*, ct.user_id FROM deposit_records dr 
            JOIN credit_transactions ct ON dr.transaction_id = ct.transaction_id 
            WHERE dr.transaction_id = ? AND dr.user_id = ?";
    $stmt = execute_query($link, $sql, "si", [$transaction_id, $_SESSION['user_id']]);
    
    if (!$stmt) {
        echo json_encode(['success' => false, 'message' => 'Database error']);
        return;
    }
    
    $result = mysqli_stmt_get_result($stmt);
    $deposit = mysqli_fetch_assoc($result);
    mysqli_stmt_close($stmt);
    
    if (!$deposit) {
        echo json_encode(['success' => false, 'message' => 'Deposit not found']);
        return;
    }
    
    if ($deposit['payment_status'] !== 'pending') {
        echo json_encode(['success' => false, 'message' => 'Deposit already processed']);
        return;
    }
    
    mysqli_begin_transaction($link);
    
    try {
        // Add credit to user's wallet
        $add_result = $credit_system->addCredit(
            $deposit['user_id'],
            $deposit['amount'],
            'paypal',
            'PayPal payment completed',
            $external_payment_id
        );
        
        if (!$add_result['success']) {
            throw new Exception($add_result['message']);
        }
        
        // Update deposit status
        $update_sql = "UPDATE deposit_records SET payment_status = 'completed', external_payment_id = ?, payment_details = ? WHERE transaction_id = ?";
        $payment_details = json_encode([
            'payment_method' => 'paypal',
            'external_id' => $external_payment_id,
            'completed_at' => date('Y-m-d H:i:s'),
            'demo_mode' => true
        ]);
        
        $update_stmt = execute_query($link, $update_sql, "sss", [$external_payment_id, $payment_details, $transaction_id]);
        
        if (!$update_stmt) {
            throw new Exception('Failed to update deposit status');
        }
        mysqli_stmt_close($update_stmt);
        
        mysqli_commit($link);
        
        echo json_encode([
            'success' => true,
            'message' => 'Payment processed successfully',
            'new_balance' => $add_result['new_balance']
        ]);
        
    } catch (Exception $e) {
        mysqli_rollback($link);
        echo json_encode(['success' => false, 'message' => $e->getMessage()]);
    }
}

/**
 * Handle real PayPal IPN (Instant Payment Notification)
 * This would be called by PayPal's servers
 */
function handlePayPalIPN() {
    // In a real implementation, you would:
    // 1. Verify the IPN with PayPal
    // 2. Extract payment details
    // 3. Update deposit status
    // 4. Add credit to user account
    
    // Example IPN verification process:
    /*
    $raw_post_data = file_get_contents('php://input');
    $raw_post_array = explode('&', $raw_post_data);
    $myPost = array();
    
    foreach ($raw_post_array as $keyval) {
        $keyval = explode('=', $keyval);
        if (count($keyval) == 2) {
            $myPost[$keyval[0]] = urldecode($keyval[1]);
        }
    }
    
    // Add 'cmd' parameter for verification
    $req = 'cmd=_notify-validate';
    foreach ($myPost as $key => $value) {
        $value = urlencode($value);
        $req .= "&$key=$value";
    }
    
    // Send back to PayPal for verification
    $ch = curl_init('https://ipnpb.paypal.com/cgi-bin/webscr'); // Use sandbox URL for testing
    curl_setopt($ch, CURLOPT_HTTP_VERSION, CURL_HTTP_VERSION_1_1);
    curl_setopt($ch, CURLOPT_POST, 1);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
    curl_setopt($ch, CURLOPT_POSTFIELDS, $req);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, 1);
    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 2);
    curl_setopt($ch, CURLOPT_FORBID_REUSE, 1);
    curl_setopt($ch, CURLOPT_HTTPHEADER, array('Connection: Close'));
    
    $res = curl_exec($ch);
    curl_close($ch);
    
    if (strcmp($res, "VERIFIED") == 0) {
        // IPN verified, process payment
        $payment_status = $_POST['payment_status'];
        $txn_id = $_POST['txn_id'];
        $custom = $_POST['custom']; // This would contain our transaction_id
        $amount = $_POST['mc_gross'];
        
        if ($payment_status == 'Completed') {
            // Process successful payment
            // Update deposit record and add credit
        }
    }
    */
    
    echo json_encode(['success' => true, 'message' => 'IPN processed']);
}
?>

<!-- PayPal Integration Documentation -->
<!--
To implement real PayPal integration:

1. Set up PayPal Developer Account:
   - Go to https://developer.paypal.com/
   - Create a new app
   - Get Client ID and Client Secret

2. Install PayPal SDK:
   composer require paypal/rest-api-sdk-php

3. Create PayPal Payment:
   $payer = new \PayPal\Api\Payer();
   $payer->setPaymentMethod('paypal');
   
   $amount = new \PayPal\Api\Amount();
   $amount->setTotal($deposit_amount);
   $amount->setCurrency('USD');
   
   $transaction = new \PayPal\Api\Transaction();
   $transaction->setAmount($amount);
   $transaction->setDescription('KMS Credit Deposit');
   $transaction->setCustom($transaction_id);
   
   $redirectUrls = new \PayPal\Api\RedirectUrls();
   $redirectUrls->setReturnUrl('http://yoursite.com/paypal_success.php');
   $redirectUrls->setCancelUrl('http://yoursite.com/paypal_cancel.php');
   
   $payment = new \PayPal\Api\Payment();
   $payment->setIntent('sale');
   $payment->setPayer($payer);
   $payment->setTransactions(array($transaction));
   $payment->setRedirectUrls($redirectUrls);
   
   $payment->create($apiContext);
   
   // Redirect user to PayPal
   header('Location: ' . $payment->getApprovalLink());

4. Handle Success/Cancel:
   - Create paypal_success.php to handle successful payments
   - Create paypal_cancel.php to handle cancelled payments
   - Execute payment and update database

5. Set up Webhooks:
   - Configure PayPal webhooks for payment notifications
   - Handle webhook events to update payment status
-->
