function initLiveChat() {
    // ---ELEMENTS---
    const chatButton = document.getElementById('liveChatButton');
    const chatWidget = document.getElementById('chatWidget');
    const closeWidgetBtn = document.getElementById('closeChatWidget');
    
    // Form elements
    const initialForm = document.getElementById('chatInitialForm');
    const guestForm = document.getElementById('guestChatForm');
    const nicknameInput = document.getElementById('chatGuestNickname');
    const emailInput = document.getElementById('chatGuestEmail');

    // Conversation elements
    const conversationView = document.getElementById('chatConversation');
    const messagesContainer = document.getElementById('chatMessages');
    const messageForm = document.getElementById('chatMessageForm');
    const messageInput = document.getElementById('chatMessageInput');

    // ---STATE---
    let sessionGuid = null;
    let lastMessageId = 0;
    let pollingInterval = null;

    // ---EVENT LISTENERS---
    if (!chatButton || !chatWidget || !closeWidgetBtn) {
        // Silently fail if elements are not on the page.
        return;
    }

    // Toggle widget visibility
    chatButton.addEventListener('click', () => {
        chatWidget.classList.toggle('visible');
    });

    closeWidgetBtn.addEventListener('click', () => {
        chatWidget.classList.remove('visible');
        resetChat();
    });

    // Handle guest form submission
    if (guestForm) {
        guestForm.addEventListener('submit', async (e) => {
            e.preventDefault();
            const nickname = nicknameInput.value.trim();
            const email = emailInput.value.trim();

            if (nickname && email) {
                try {
                    const response = await fetch('PHP/chat_start.php', {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({ nickname, email })
                    });
                    const data = await response.json();

                    if (data.success) {
                        sessionGuid = data.session_guid;
                        showConversationView();
                    } else {
                        alert('Error starting chat: ' + data.message);
                    }
                } catch (error) {
                    console.error('Failed to start chat session:', error);
                    alert('An error occurred. Please try again.');
                }
            }
        });
    }
    
    // Handle message form submission
    if (messageForm) {
        messageForm.addEventListener('submit', async (e) => {
            e.preventDefault();
            const message = messageInput.value.trim();
            if (message && sessionGuid) {
                addMessageToUI('user', message);
                messageInput.value = '';

                try {
                    await fetch('PHP/chat_send_message.php', {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({
                            session_guid: sessionGuid,
                            sender: 'user',
                            message: message
                        })
                    });
                } catch (error) {
                    console.error('Failed to send message:', error);
                }
            }
        });
    }


    // ---FUNCTIONS---

    function showConversationView() {
        if(initialForm) initialForm.style.display = 'none';
        if(conversationView) conversationView.style.display = 'flex';
        startPolling();
    }

    function resetChat() {
        if (pollingInterval) clearInterval(pollingInterval);
        sessionGuid = null;
        lastMessageId = 0;
        pollingInterval = null;
        if(messagesContainer) messagesContainer.innerHTML = '';
        if(initialForm) initialForm.style.display = 'flex';
        if(conversationView) conversationView.style.display = 'none';
        if(guestForm) guestForm.reset();
        if(messageForm) messageForm.reset();
    }

    function addMessageToUI(sender, message) {
        const messageEl = document.createElement('div');
        messageEl.classList.add('chat-message', sender);
        messageEl.textContent = message;
        if(messagesContainer) {
            messagesContainer.appendChild(messageEl);
            messagesContainer.scrollTop = messagesContainer.scrollHeight;
        }
    }

    function startPolling() {
        if (pollingInterval) clearInterval(pollingInterval);

        pollingInterval = setInterval(async () => {
            if (!sessionGuid) return;
            try {
                const response = await fetch(`PHP/chat_get_messages.php?session_guid=${sessionGuid}&last_message_id=${lastMessageId}`);
                const data = await response.json();

                if (data.success && data.messages.length > 0) {
                    data.messages.forEach(msg => {
                        addMessageToUI(msg.sender, msg.message);
                        lastMessageId = msg.id;
                    });
                }
            } catch (error) {
                console.error('Polling error:', error);
            }
        }, 3000);
    }
}