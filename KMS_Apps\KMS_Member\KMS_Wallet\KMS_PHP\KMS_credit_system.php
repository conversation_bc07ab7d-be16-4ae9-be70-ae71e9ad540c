<?php
/**
 * KMS Credit System Core Functions
 * Handles all credit-related operations including balance management, transactions, deposits, and transfers
 */

require_once '../../../KMS_Core/KMS_Config/KMS_PHP/KMS_config.php';
require_once '../../../KMS_Core/KMS_Functions/KMS_PHP/KMS_functions.php';

class KMSCreditSystem {
    private $db;
    
    public function __construct($database_connection) {
        $this->db = $database_connection;
    }
    
    /**
     * Get user's wallet information
     */
    public function getUserWallet($user_id) {
        $sql = "SELECT * FROM user_wallets WHERE user_id = ?";
        $stmt = execute_query($this->db, $sql, "i", [$user_id]);
        
        if ($stmt) {
            $result = mysqli_stmt_get_result($stmt);
            $wallet = mysqli_fetch_assoc($result);
            mysqli_stmt_close($stmt);
            
            if (!$wallet) {
                // Create wallet if it doesn't exist
                $this->createUserWallet($user_id);
                return $this->getUserWallet($user_id);
            }
            
            return $wallet;
        }
        
        return false;
    }
    
    /**
     * Create a new wallet for user
     */
    private function createUserWallet($user_id) {
        $sql = "INSERT INTO user_wallets (user_id, balance) VALUES (?, 0.00)";
        $stmt = execute_query($this->db, $sql, "i", [$user_id]);
        
        if ($stmt) {
            mysqli_stmt_close($stmt);
            return true;
        }
        
        return false;
    }
    
    /**
     * Generate unique transaction ID
     */
    private function generateTransactionId() {
        return 'KMS' . date('Ymd') . strtoupper(substr(uniqid(), -8));
    }
    
    /**
     * Add credit to user's wallet (deposit or admin gift)
     */
    public function addCredit($user_id, $amount, $payment_method, $description = '', $external_transaction_id = null, $admin_user_id = null, $transaction_type = 'deposit') {
        if ($amount <= 0) {
            return ['success' => false, 'message' => 'Invalid amount'];
        }
        
        mysqli_begin_transaction($this->db);
        
        try {
            // Get current wallet
            $wallet = $this->getUserWallet($user_id);
            if (!$wallet) {
                throw new Exception('Wallet not found');
            }
            
            $transaction_id = $this->generateTransactionId();
            $balance_before = $wallet['balance'];
            $balance_after = $balance_before + $amount;
            
            // Update wallet balance
            $update_sql = "UPDATE user_wallets SET balance = ?, total_deposited = total_deposited + ?, updated_at = CURRENT_TIMESTAMP WHERE user_id = ?";
            $update_stmt = execute_query($this->db, $update_sql, "ddi", [$balance_after, $amount, $user_id]);
            
            if (!$update_stmt) {
                throw new Exception('Failed to update wallet');
            }
            mysqli_stmt_close($update_stmt);
            
            // Record transaction
            $reference_type = ($transaction_type === 'admin_gift') ? 'admin' : 'deposit';
            $trans_sql = "INSERT INTO credit_transactions (transaction_id, user_id, transaction_type, amount, balance_before, balance_after, status, description, reference_type, payment_method, external_transaction_id, admin_user_id) VALUES (?, ?, ?, ?, ?, ?, 'completed', ?, ?, ?, ?, ?)";
            $trans_stmt = execute_query($this->db, $trans_sql, "sisddsssssi", [$transaction_id, $user_id, $transaction_type, $amount, $balance_before, $balance_after, $description, $reference_type, $payment_method, $external_transaction_id, $admin_user_id]);
            
            if (!$trans_stmt) {
                throw new Exception('Failed to record transaction');
            }
            mysqli_stmt_close($trans_stmt);

            // Record deposit details only for actual deposits
            if ($transaction_type === 'deposit') {
                $deposit_sql = "INSERT INTO deposit_records (user_id, transaction_id, amount, payment_method, payment_status, external_payment_id, processed_by) VALUES (?, ?, ?, ?, 'completed', ?, ?)";
                $deposit_stmt = execute_query($this->db, $deposit_sql, "isdssi", [$user_id, $transaction_id, $amount, $payment_method, $external_transaction_id, $admin_user_id]);

                if (!$deposit_stmt) {
                    throw new Exception('Failed to record deposit');
                }
                mysqli_stmt_close($deposit_stmt);
            }
            
            mysqli_commit($this->db);
            
            return [
                'success' => true,
                'message' => 'Credit added successfully',
                'transaction_id' => $transaction_id,
                'new_balance' => $balance_after
            ];
            
        } catch (Exception $e) {
            mysqli_rollback($this->db);
            return ['success' => false, 'message' => $e->getMessage()];
        }
    }
    
    /**
     * Deduct credit from user's wallet (spend or admin deduct)
     */
    public function deductCredit($user_id, $amount, $description = '', $reference_type = null, $reference_id = null, $admin_user_id = null, $transaction_type = 'spend') {
        if ($amount <= 0) {
            return ['success' => false, 'message' => 'Invalid amount'];
        }
        
        mysqli_begin_transaction($this->db);
        
        try {
            // Get current wallet with lock
            $wallet_sql = "SELECT * FROM user_wallets WHERE user_id = ? FOR UPDATE";
            $wallet_stmt = execute_query($this->db, $wallet_sql, "i", [$user_id]);
            
            if (!$wallet_stmt) {
                throw new Exception('Failed to get wallet');
            }
            
            $result = mysqli_stmt_get_result($wallet_stmt);
            $wallet = mysqli_fetch_assoc($result);
            mysqli_stmt_close($wallet_stmt);
            
            if (!$wallet) {
                throw new Exception('Wallet not found');
            }
            
            if ($wallet['balance'] < $amount) {
                throw new Exception('Insufficient balance');
            }
            
            $transaction_id = $this->generateTransactionId();
            $balance_before = $wallet['balance'];
            $balance_after = $balance_before - $amount;
            
            // Update wallet balance
            $update_sql = "UPDATE user_wallets SET balance = ?, total_spent = total_spent + ?, updated_at = CURRENT_TIMESTAMP WHERE user_id = ?";
            $update_stmt = execute_query($this->db, $update_sql, "ddi", [$balance_after, $amount, $user_id]);
            
            if (!$update_stmt) {
                throw new Exception('Failed to update wallet');
            }
            mysqli_stmt_close($update_stmt);
            
            // Record transaction
            $trans_sql = "INSERT INTO credit_transactions (transaction_id, user_id, transaction_type, amount, balance_before, balance_after, status, description, reference_type, reference_id, admin_user_id) VALUES (?, ?, ?, ?, ?, ?, 'completed', ?, ?, ?, ?)";
            $trans_stmt = execute_query($this->db, $trans_sql, "sisddsssi", [$transaction_id, $user_id, $transaction_type, $amount, $balance_before, $balance_after, $description, $reference_type, $reference_id, $admin_user_id]);
            
            if (!$trans_stmt) {
                throw new Exception('Failed to record transaction');
            }
            mysqli_stmt_close($trans_stmt);
            
            mysqli_commit($this->db);
            
            return [
                'success' => true,
                'message' => 'Credit deducted successfully',
                'transaction_id' => $transaction_id,
                'new_balance' => $balance_after
            ];
            
        } catch (Exception $e) {
            mysqli_rollback($this->db);
            return ['success' => false, 'message' => $e->getMessage()];
        }
    }
    
    /**
     * Transfer credit between users
     */
    public function transferCredit($from_user_id, $to_user_id, $amount, $message = '') {
        if ($amount <= 0) {
            return ['success' => false, 'message' => 'Invalid amount'];
        }
        
        if ($from_user_id == $to_user_id) {
            return ['success' => false, 'message' => 'Cannot transfer to yourself'];
        }
        
        // Check transfer limits
        $settings = $this->getSystemSettings();
        if ($amount < $settings['min_transfer_amount']) {
            return ['success' => false, 'message' => 'Amount below minimum transfer limit'];
        }
        
        if ($amount > $settings['max_transfer_amount']) {
            return ['success' => false, 'message' => 'Amount exceeds maximum transfer limit'];
        }
        
        mysqli_begin_transaction($this->db);
        
        try {
            // Check sender's balance
            $sender_wallet = $this->getUserWallet($from_user_id);
            if (!$sender_wallet || $sender_wallet['balance'] < $amount) {
                throw new Exception('Insufficient balance');
            }
            
            // Check recipient exists
            $recipient_wallet = $this->getUserWallet($to_user_id);
            if (!$recipient_wallet) {
                throw new Exception('Recipient wallet not found');
            }
            
            $transfer_id = $this->generateTransactionId();
            
            // Deduct from sender
            $deduct_result = $this->deductCredit($from_user_id, $amount, "Transfer to user ID: $to_user_id", 'transfer', null);
            if (!$deduct_result['success']) {
                throw new Exception($deduct_result['message']);
            }
            
            // Add to recipient
            $add_result = $this->addCredit($to_user_id, $amount, 'transfer', "Transfer from user ID: $from_user_id", $transfer_id);
            if (!$add_result['success']) {
                throw new Exception($add_result['message']);
            }
            
            // Record transfer
            $transfer_sql = "INSERT INTO transfer_records (transfer_id, from_user_id, to_user_id, amount, status, message) VALUES (?, ?, ?, ?, 'completed', ?)";
            $transfer_stmt = execute_query($this->db, $transfer_sql, "siids", [$transfer_id, $from_user_id, $to_user_id, $amount, $message]);
            
            if (!$transfer_stmt) {
                throw new Exception('Failed to record transfer');
            }
            mysqli_stmt_close($transfer_stmt);
            
            mysqli_commit($this->db);
            
            return [
                'success' => true,
                'message' => 'Transfer completed successfully',
                'transfer_id' => $transfer_id
            ];
            
        } catch (Exception $e) {
            mysqli_rollback($this->db);
            return ['success' => false, 'message' => $e->getMessage()];
        }
    }
    
    /**
     * Get user's transaction history
     */
    public function getUserTransactions($user_id, $limit = 50, $offset = 0) {
        $sql = "SELECT * FROM credit_transactions WHERE user_id = ? ORDER BY created_at DESC LIMIT ? OFFSET ?";
        $stmt = execute_query($this->db, $sql, "iii", [$user_id, $limit, $offset]);
        
        if ($stmt) {
            $result = mysqli_stmt_get_result($stmt);
            $transactions = [];
            
            while ($row = mysqli_fetch_assoc($result)) {
                $transactions[] = $row;
            }
            
            mysqli_stmt_close($stmt);
            return $transactions;
        }
        
        return [];
    }
    
    /**
     * Get system settings
     */
    private function getSystemSettings() {
        $sql = "SELECT setting_key, setting_value FROM credit_system_settings";
        $result = mysqli_query($this->db, $sql);
        
        $settings = [
            'min_transfer_amount' => 1.00,
            'max_transfer_amount' => 1000.00,
            'daily_transfer_limit' => 5000.00,
            'require_transfer_approval' => false,
            'system_fee_percentage' => 0.00,
            'maintenance_mode' => false
        ];
        
        if ($result) {
            while ($row = mysqli_fetch_assoc($result)) {
                $value = $row['setting_value'];
                
                // Convert to appropriate type
                if (is_numeric($value)) {
                    $value = (float)$value;
                } elseif ($value === 'true' || $value === 'false') {
                    $value = ($value === 'true');
                }
                
                $settings[$row['setting_key']] = $value;
            }
        }
        
        return $settings;
    }
    
    /**
     * Get available payment methods
     */
    public function getPaymentMethods() {
        $sql = "SELECT * FROM payment_methods WHERE is_active = TRUE ORDER BY display_name";
        $result = mysqli_query($this->db, $sql);
        
        $methods = [];
        if ($result) {
            while ($row = mysqli_fetch_assoc($result)) {
                $methods[] = $row;
            }
        }
        
        return $methods;
    }
}

// Initialize credit system
$credit_system = new KMSCreditSystem($link);
?>
