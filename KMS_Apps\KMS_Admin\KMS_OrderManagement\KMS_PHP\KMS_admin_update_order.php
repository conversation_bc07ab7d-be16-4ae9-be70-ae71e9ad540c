<?php
require_once 'config.php';
require_once 'affiliate_tracker.php';

header('Content-Type: application/json');

// Admin Check
if (!isset($_SESSION['user_id']) || !isset($_SESSION['is_admin']) || !$_SESSION['is_admin']) {
    echo json_encode(['success' => false, 'message' => 'Access denied.']);
    exit;
}

// Check for required POST data
$required_params = ['order_id', 'status'];
foreach ($required_params as $param) {
    if (!isset($_POST[$param])) {
        echo json_encode(['success' => false, 'message' => "Missing required parameter: $param"]);
        exit;
    }
}

// Sanitize and validate inputs
$order_id = filter_var($_POST['order_id'], FILTER_VALIDATE_INT);
$status = $_POST['status'];
$final_price = !empty($_POST['final_price']) ? filter_var($_POST['final_price'], FILTER_VALIDATE_FLOAT) : null;
$eta = !empty($_POST['estimated_completion_date']) ? $_POST['estimated_completion_date'] : null;

if ($order_id === false) {
    echo json_encode(['success' => false, 'message' => 'Invalid Order ID.']);
    exit;
}
if ($final_price === false) {
    echo json_encode(['success' => false, 'message' => 'Invalid Price format.']);
    exit;
}

// Validate status against the ENUM list
$allowed_statuses = ['Quote Requested', 'Quote Provided', 'Order Pending', 'Payment Received', 'Order in Progress', 'Order Shipped', 'Order Delivered', 'Order Cancelled'];
if (!in_array($status, $allowed_statuses)) {
    echo json_encode(['success' => false, 'message' => 'Invalid status value.']);
    exit;
}

// Validate date format if provided
if ($eta !== null && !DateTime::createFromFormat('Y-m-d', $eta)) {
    echo json_encode(['success' => false, 'message' => 'Invalid ETA date format. Please use YYYY-MM-DD.']);
    exit;
}


// Get current order status before update to check if it's changing to completed
$current_status_sql = "SELECT status FROM pc_orders WHERE id = ?";
$current_stmt = execute_query($link, $current_status_sql, "i", [$order_id]);
$current_status = null;

if ($current_stmt) {
    $result = mysqli_stmt_get_result($current_stmt);
    $row = mysqli_fetch_assoc($result);
    $current_status = $row['status'] ?? null;
    mysqli_stmt_close($current_stmt);
}

// Prepare the UPDATE query
$sql = "UPDATE pc_orders SET status = ?, final_price = ?, estimated_completion_date = ? WHERE id = ?";
$stmt = execute_query($link, $sql, "sdsi", [$status, $final_price, $eta, $order_id]);

if ($stmt) {
    if (mysqli_stmt_affected_rows($stmt) > 0) {
        // Check if order status changed to completed and process affiliate commission
        if ($current_status !== 'Order Delivered' && $status === 'Order Delivered') {
            // Order just completed, process affiliate commission
            $commission_result = processAffiliateOrderCommission($order_id, 'pc');

            if ($commission_result && $commission_result['success']) {
                error_log("Affiliate commission processed for PC order $order_id: $" . $commission_result['amount']);
            }
        }

        echo json_encode(['success' => true, 'message' => 'Order updated successfully.']);
    } else {
        // This can happen if the data submitted was the same as the data in the DB
        echo json_encode(['success' => true, 'message' => 'No changes were made to the order.']);
    }
    mysqli_stmt_close($stmt);
} else {
    echo json_encode(['success' => false, 'message' => 'Failed to update order.']);
}

close_db_connection($link);

?>