<?php
/**
 * KMS Credit Deposit API
 * Handles credit deposit/top-up operations
 */

require_once 'config.php';
require_once 'credit_system.php';
require_once 'credit_security.php';

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    echo json_encode(['success' => false, 'message' => 'User not logged in']);
    exit;
}

$user_id = $_SESSION['user_id'];
$action = $_POST['action'] ?? $_GET['action'] ?? '';

header('Content-Type: application/json');

switch ($action) {
    case 'create_deposit':
        createDeposit($credit_system, $user_id);
        break;
        
    case 'confirm_deposit':
        confirmDeposit($credit_system, $user_id);
        break;
        
    case 'get_deposit_history':
        getDepositHistory($credit_system, $user_id);
        break;
        
    case 'cancel_deposit':
        cancelDeposit($credit_system, $user_id);
        break;
        
    default:
        echo json_encode(['success' => false, 'message' => 'Invalid action']);
        break;
}

/**
 * Create a new deposit request
 */
function createDeposit($credit_system, $user_id) {
    $amount = floatval($_POST['amount'] ?? 0);
    $payment_method = trim($_POST['payment_method'] ?? '');
    
    if ($amount <= 0) {
        echo json_encode(['success' => false, 'message' => 'Invalid deposit amount']);
        return;
    }
    
    if (empty($payment_method)) {
        echo json_encode(['success' => false, 'message' => 'Payment method is required']);
        return;
    }
    
    // Validate payment method
    $methods = $credit_system->getPaymentMethods();
    $valid_method = null;
    
    foreach ($methods as $method) {
        if ($method['method_name'] === $payment_method) {
            $valid_method = $method;
            break;
        }
    }
    
    if (!$valid_method) {
        echo json_encode(['success' => false, 'message' => 'Invalid payment method']);
        return;
    }
    
    // Check amount limits
    if ($amount < $valid_method['min_amount']) {
        echo json_encode([
            'success' => false, 
            'message' => "Minimum deposit amount is $" . number_format($valid_method['min_amount'], 2)
        ]);
        return;
    }
    
    if ($amount > $valid_method['max_amount']) {
        echo json_encode([
            'success' => false, 
            'message' => "Maximum deposit amount is $" . number_format($valid_method['max_amount'], 2)
        ]);
        return;
    }
    
    // Calculate fees
    $fee = calculateFee($amount, $valid_method);
    $total_amount = $amount + $fee;
    
    // Create pending deposit record
    global $link;
    $transaction_id = 'KMS' . date('Ymd') . strtoupper(substr(uniqid(), -8));
    
    mysqli_begin_transaction($link);
    
    try {
        // Create transaction record
        $trans_sql = "INSERT INTO credit_transactions (transaction_id, user_id, transaction_type, amount, balance_before, balance_after, status, description, reference_type, payment_method) VALUES (?, ?, 'deposit', ?, 0, 0, 'pending', ?, 'deposit', ?)";
        
        $wallet = $credit_system->getUserWallet($user_id);
        $balance_before = $wallet['balance'];
        
        $trans_stmt = execute_query($link, $trans_sql, "sidss", [$transaction_id, $user_id, $amount, "Deposit via " . $valid_method['display_name'], $payment_method]);
        
        if (!$trans_stmt) {
            throw new Exception('Failed to create transaction record');
        }
        mysqli_stmt_close($trans_stmt);
        
        // Create deposit record
        $deposit_sql = "INSERT INTO deposit_records (user_id, transaction_id, amount, payment_method, payment_status) VALUES (?, ?, ?, ?, 'pending')";
        $deposit_stmt = execute_query($link, $deposit_sql, "isds", [$user_id, $transaction_id, $amount, $payment_method]);
        
        if (!$deposit_stmt) {
            throw new Exception('Failed to create deposit record');
        }
        mysqli_stmt_close($deposit_stmt);
        
        mysqli_commit($link);
        
        // Generate payment URL/instructions based on method
        $payment_info = generatePaymentInfo($payment_method, $amount, $fee, $transaction_id, $user_id);
        
        echo json_encode([
            'success' => true,
            'message' => 'Deposit request created successfully',
            'transaction_id' => $transaction_id,
            'amount' => number_format($amount, 2),
            'fee' => number_format($fee, 2),
            'total_amount' => number_format($total_amount, 2),
            'payment_method' => $valid_method['display_name'],
            'payment_info' => $payment_info
        ]);
        
    } catch (Exception $e) {
        mysqli_rollback($link);
        echo json_encode(['success' => false, 'message' => $e->getMessage()]);
    }
}

/**
 * Confirm a deposit (for manual payment methods)
 */
function confirmDeposit($credit_system, $user_id) {
    $transaction_id = trim($_POST['transaction_id'] ?? '');
    $payment_proof = trim($_POST['payment_proof'] ?? '');
    
    if (empty($transaction_id)) {
        echo json_encode(['success' => false, 'message' => 'Transaction ID is required']);
        return;
    }
    
    global $link;
    
    // Check if deposit exists and belongs to user
    $sql = "SELECT dr.*, ct.status as transaction_status FROM deposit_records dr 
            JOIN credit_transactions ct ON dr.transaction_id = ct.transaction_id 
            WHERE dr.transaction_id = ? AND dr.user_id = ?";
    $stmt = execute_query($link, $sql, "si", [$transaction_id, $user_id]);
    
    if (!$stmt) {
        echo json_encode(['success' => false, 'message' => 'Database error']);
        return;
    }
    
    $result = mysqli_stmt_get_result($stmt);
    $deposit = mysqli_fetch_assoc($result);
    mysqli_stmt_close($stmt);
    
    if (!$deposit) {
        echo json_encode(['success' => false, 'message' => 'Deposit not found']);
        return;
    }
    
    if ($deposit['payment_status'] !== 'pending') {
        echo json_encode(['success' => false, 'message' => 'Deposit already processed']);
        return;
    }
    
    // Update deposit with payment proof
    $update_sql = "UPDATE deposit_records SET payment_details = ?, payment_status = 'processing' WHERE transaction_id = ?";
    $payment_details = json_encode(['payment_proof' => $payment_proof, 'submitted_at' => date('Y-m-d H:i:s')]);
    $update_stmt = execute_query($link, $update_sql, "ss", [$payment_details, $transaction_id]);
    
    if ($update_stmt) {
        mysqli_stmt_close($update_stmt);
        
        echo json_encode([
            'success' => true,
            'message' => 'Payment confirmation submitted. Your deposit will be processed within 24 hours.'
        ]);
    } else {
        echo json_encode(['success' => false, 'message' => 'Failed to submit payment confirmation']);
    }
}

/**
 * Get user's deposit history
 */
function getDepositHistory($credit_system, $user_id) {
    global $link;
    
    $page = intval($_GET['page'] ?? 1);
    $limit = intval($_GET['limit'] ?? 20);
    $offset = ($page - 1) * $limit;
    
    $sql = "SELECT dr.*, pm.display_name as payment_method_name 
            FROM deposit_records dr 
            LEFT JOIN payment_methods pm ON dr.payment_method = pm.method_name 
            WHERE dr.user_id = ? 
            ORDER BY dr.created_at DESC 
            LIMIT ? OFFSET ?";
    
    $stmt = execute_query($link, $sql, "iii", [$user_id, $limit, $offset]);
    
    if (!$stmt) {
        echo json_encode(['success' => false, 'message' => 'Database error']);
        return;
    }
    
    $result = mysqli_stmt_get_result($stmt);
    $deposits = [];
    
    while ($row = mysqli_fetch_assoc($result)) {
        $deposits[] = [
            'id' => $row['id'],
            'transaction_id' => $row['transaction_id'],
            'amount' => number_format($row['amount'], 2),
            'payment_method' => $row['payment_method_name'] ?? $row['payment_method'],
            'status' => $row['payment_status'],
            'status_display' => getDepositStatusDisplay($row['payment_status']),
            'created_at' => date('Y-m-d H:i:s', strtotime($row['created_at'])),
            'external_payment_id' => $row['external_payment_id']
        ];
    }
    
    mysqli_stmt_close($stmt);
    
    echo json_encode([
        'success' => true,
        'deposits' => $deposits,
        'pagination' => [
            'page' => $page,
            'limit' => $limit,
            'has_more' => count($deposits) == $limit
        ]
    ]);
}

/**
 * Cancel a pending deposit
 */
function cancelDeposit($credit_system, $user_id) {
    $transaction_id = trim($_POST['transaction_id'] ?? '');
    
    if (empty($transaction_id)) {
        echo json_encode(['success' => false, 'message' => 'Transaction ID is required']);
        return;
    }
    
    global $link;
    
    mysqli_begin_transaction($link);
    
    try {
        // Update deposit status
        $deposit_sql = "UPDATE deposit_records SET payment_status = 'cancelled' WHERE transaction_id = ? AND user_id = ? AND payment_status = 'pending'";
        $deposit_stmt = execute_query($link, $deposit_sql, "si", [$transaction_id, $user_id]);
        
        if (!$deposit_stmt || mysqli_stmt_affected_rows($deposit_stmt) == 0) {
            throw new Exception('Deposit not found or cannot be cancelled');
        }
        mysqli_stmt_close($deposit_stmt);
        
        // Update transaction status
        $trans_sql = "UPDATE credit_transactions SET status = 'cancelled' WHERE transaction_id = ? AND user_id = ?";
        $trans_stmt = execute_query($link, $trans_sql, "si", [$transaction_id, $user_id]);
        
        if (!$trans_stmt) {
            throw new Exception('Failed to update transaction status');
        }
        mysqli_stmt_close($trans_stmt);
        
        mysqli_commit($link);
        
        echo json_encode([
            'success' => true,
            'message' => 'Deposit cancelled successfully'
        ]);
        
    } catch (Exception $e) {
        mysqli_rollback($link);
        echo json_encode(['success' => false, 'message' => $e->getMessage()]);
    }
}

/**
 * Calculate fee for deposit
 */
function calculateFee($amount, $payment_method) {
    $fee = ($amount * $payment_method['fee_percentage'] / 100) + $payment_method['fee_fixed'];
    return round($fee, 2);
}

/**
 * Generate payment information based on method
 */
function generatePaymentInfo($payment_method, $amount, $fee, $transaction_id, $user_id) {
    switch ($payment_method) {
        case 'paypal':
            return [
                'type' => 'paypal',
                'instructions' => 'You will be redirected to PayPal to complete your payment.',
                'redirect_url' => 'paypal_redirect.php?transaction_id=' . $transaction_id
            ];
            
        case 'stripe':
            return [
                'type' => 'stripe',
                'instructions' => 'You will be redirected to our secure payment page.',
                'redirect_url' => 'stripe_redirect.php?transaction_id=' . $transaction_id
            ];
            
        case 'bank_transfer':
            return [
                'type' => 'bank_transfer',
                'instructions' => 'Please transfer the exact amount to our bank account and upload payment proof.',
                'bank_details' => [
                    'bank_name' => 'KMS Bank',
                    'account_number' => '**********',
                    'account_name' => 'KelvinKMS.com',
                    'reference' => $transaction_id
                ]
            ];
            
        default:
            return [
                'type' => 'manual',
                'instructions' => 'Please contact support for payment instructions.'
            ];
    }
}

/**
 * Get display text for deposit status
 */
function getDepositStatusDisplay($status) {
    $statuses = [
        'pending' => 'Pending',
        'processing' => 'Processing',
        'completed' => 'Completed',
        'failed' => 'Failed',
        'cancelled' => 'Cancelled'
    ];
    
    return $statuses[$status] ?? ucfirst($status);
}
?>
