<?php
header('Content-Type: application/json');
require_once 'config.php';
require_once 'functions.php';

// Check if user is logged in
if (!isset($_SESSION["loggedin"]) || $_SESSION["loggedin"] !== true) {
    echo json_encode(['success' => false, 'message' => 'Please login first.']);
    exit;
}

if ($_SERVER["REQUEST_METHOD"] == "POST") {
    $user_id = $_SESSION["id"];
    $username = $_SESSION["username"];
    $services = $_POST['services'] ?? '[]';
    $notes = sanitize_input($_POST['notes'] ?? '');

    // New fields for VIP customization
    $vip_customization = null;
    $estimated_price = null;

    if (isset($_POST['vip_customization'])) {
        $vip_data = json_decode($_POST['vip_customization'], true);
        if ($vip_data && is_array($vip_data)) {
            $vip_customization = json_encode($vip_data); // Store the full JSON
            if (isset($vip_data['estimated_price'])) {
                $estimated_price = (float)$vip_data['estimated_price'];
            }
        }
    }

    // Validate services
    if (empty($services)) {
        echo json_encode(['success' => false, 'message' => 'Please select at least one service.']);
        exit;
    }
    
    $services_array = json_decode($services, true);
    if (!is_array($services_array) || empty($services_array)) {
        echo json_encode(['success' => false, 'message' => 'Invalid services data.']);
        exit;
    }
    
    // Insert order into database
    $sql = "INSERT INTO orders (user_id, username, services, notes, vip_customization, estimated_price) VALUES (?, ?, ?, ?, ?, ?)";
    $stmt = execute_query($link, $sql, "issssd", [$user_id, $username, $services, $notes, $vip_customization, $estimated_price]);
    
    if ($stmt) {
        mysqli_stmt_close($stmt);
        echo json_encode(['success' => true, 'message' => 'Order submitted successfully!']);
    } else {
        echo json_encode([
            'success' => false,
            'message' => 'Failed to submit order. Please try again later.'
        ]);
    }
    
    close_db_connection($link);
} else {
    echo json_encode([
        'success' => false,
        'message' => 'Invalid request method.'
    ]);
}
?>
