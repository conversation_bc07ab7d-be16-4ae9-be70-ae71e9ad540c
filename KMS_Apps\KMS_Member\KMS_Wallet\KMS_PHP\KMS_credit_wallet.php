<?php
/**
 * KMS Credit Wallet API
 * Handles wallet-related operations for users
 */

session_start();
require_once 'config.php';
require_once 'credit_system.php';
// require_once 'credit_security.php';

// Check if user is logged in
if (!isset($_SESSION['id'])) {
    echo json_encode(['success' => false, 'message' => 'User not logged in']);
    exit;
}

$user_id = $_SESSION['id'];
$action = $_POST['action'] ?? $_GET['action'] ?? '';

header('Content-Type: application/json');

switch ($action) {
    case 'get_wallet':
        getWalletInfo($credit_system, $user_id);
        break;
        
    case 'get_transactions':
        getTransactionHistory($credit_system, $user_id);
        break;
        
    case 'transfer':
        processTransfer($credit_system, $user_id);
        break;
        
    case 'get_payment_methods':
        getPaymentMethods($credit_system);
        break;
        
    default:
        echo json_encode(['success' => false, 'message' => 'Invalid action']);
        break;
}

/**
 * Get user's wallet information
 */
function getWalletInfo($credit_system, $user_id) {
    $wallet = $credit_system->getUserWallet($user_id);
    
    if ($wallet) {
        echo json_encode([
            'success' => true,
            'balance' => number_format($wallet['balance'], 2),
            'frozen_balance' => number_format($wallet['frozen_balance'] ?? 0, 2),
            'total_deposited' => number_format($wallet['total_deposited'] ?? 0, 2),
            'total_spent' => number_format($wallet['total_spent'] ?? 0, 2),
            'available_balance' => number_format($wallet['balance'] - ($wallet['frozen_balance'] ?? 0), 2)
        ]);
    } else {
        echo json_encode(['success' => false, 'message' => 'Failed to get wallet information']);
    }
}

/**
 * Get user's transaction history
 */
function getTransactionHistory($credit_system, $user_id) {
    $page = intval($_GET['page'] ?? 1);
    $limit = intval($_GET['limit'] ?? 20);
    $offset = ($page - 1) * $limit;
    
    $transactions = $credit_system->getUserTransactions($user_id, $limit, $offset);
    
    // Format transactions for display
    $formatted_transactions = [];
    foreach ($transactions as $transaction) {
        $formatted_transactions[] = [
            'id' => $transaction['id'],
            'transaction_id' => $transaction['transaction_id'],
            'type' => $transaction['transaction_type'],
            'amount' => number_format($transaction['amount'], 2),
            'balance_before' => number_format($transaction['balance_before'], 2),
            'balance_after' => number_format($transaction['balance_after'], 2),
            'status' => $transaction['status'],
            'description' => $transaction['description'],
            'payment_method' => $transaction['payment_method'],
            'created_at' => date('Y-m-d H:i:s', strtotime($transaction['created_at'])),
            'type_display' => getTransactionTypeDisplay($transaction['transaction_type']),
            'amount_display' => getAmountDisplay($transaction['transaction_type'], $transaction['amount'])
        ];
    }
    
    echo json_encode([
        'success' => true,
        'transactions' => $formatted_transactions,
        'pagination' => [
            'page' => $page,
            'limit' => $limit,
            'has_more' => count($transactions) == $limit
        ]
    ]);
}

/**
 * Process credit transfer between users
 */
function processTransfer($credit_system, $user_id) {
    $to_username = trim($_POST['to_username'] ?? '');
    $amount = floatval($_POST['amount'] ?? 0);
    $message = trim($_POST['message'] ?? '');

    if (empty($to_username)) {
        echo json_encode(['success' => false, 'message' => 'Recipient username is required']);
        return;
    }

    if ($amount <= 0) {
        echo json_encode(['success' => false, 'message' => 'Invalid transfer amount']);
        return;
    }

    // Basic security checks (simplified)
    if ($amount > 1000) {
        echo json_encode(['success' => false, 'message' => 'Transfer amount exceeds maximum limit']);
        return;
    }
    
    // Get recipient user ID
    global $link;
    $sql = "SELECT id FROM users WHERE username = ? AND is_active = TRUE";
    $stmt = execute_query($link, $sql, "s", [$to_username]);
    
    if (!$stmt) {
        echo json_encode(['success' => false, 'message' => 'Database error']);
        return;
    }
    
    $result = mysqli_stmt_get_result($stmt);
    $recipient = mysqli_fetch_assoc($result);
    mysqli_stmt_close($stmt);
    
    if (!$recipient) {
        echo json_encode(['success' => false, 'message' => 'Recipient user not found']);
        return;
    }
    
    $to_user_id = $recipient['id'];
    
    // Process transfer
    $result = $credit_system->transferCredit($user_id, $to_user_id, $amount, $message);
    
    echo json_encode($result);
}

/**
 * Get available payment methods
 */
function getPaymentMethods($credit_system) {
    $methods = $credit_system->getPaymentMethods();
    
    echo json_encode([
        'success' => true,
        'payment_methods' => $methods
    ]);
}

/**
 * Get display text for transaction type
 */
function getTransactionTypeDisplay($type) {
    $types = [
        'deposit' => 'Deposit',
        'withdraw' => 'Withdrawal',
        'spend' => 'Purchase',
        'refund' => 'Refund',
        'transfer_in' => 'Transfer Received',
        'transfer_out' => 'Transfer Sent',
        'admin_adjust' => 'Admin Adjustment'
    ];
    
    return $types[$type] ?? ucfirst($type);
}

/**
 * Get formatted amount display with +/- prefix
 */
function getAmountDisplay($type, $amount) {
    $credit_types = ['deposit', 'refund', 'transfer_in', 'admin_adjust'];
    $debit_types = ['withdraw', 'spend', 'transfer_out'];
    
    if (in_array($type, $credit_types)) {
        return '+$' . number_format($amount, 2);
    } elseif (in_array($type, $debit_types)) {
        return '-$' . number_format($amount, 2);
    } else {
        return '$' . number_format($amount, 2);
    }
}
?>
