<?php
/**
 * Service Prices API
 * Handles CRUD operations for service pricing management
 */

session_start();
require_once 'config.php';
require_once 'functions.php';

header('Content-Type: application/json');

// Check if user is logged in for admin operations
function checkAdminAccess() {
    if (!isset($_SESSION["loggedin"]) || $_SESSION["loggedin"] !== true || !isset($_SESSION["is_admin"]) || !$_SESSION["is_admin"]) {
        echo json_encode(['success' => false, 'message' => 'Admin access required']);
        exit;
    }
}

// Handle different API actions
$action = $_GET['action'] ?? $_POST['action'] ?? '';

switch ($action) {
    case 'get_prices':
        getPrices();
        break;
    case 'get_price':
        getPrice();
        break;
    case 'update_price':
        checkAdminAccess();
        updatePrice();
        break;
    case 'add_price':
        checkAdminAccess();
        addPrice();
        break;
    case 'delete_price':
        checkAdminAccess();
        deletePrice();
        break;
    case 'toggle_active':
        checkAdminAccess();
        toggleActive();
        break;
    case 'update_sort_orders':
        checkAdminAccess();
        updateSortOrders();
        break;
    default:
        echo json_encode(['success' => false, 'message' => 'Invalid action']);
}

/**
 * Get all prices or prices by category
 */
function getPrices() {
    // Create a new dedicated connection for this operation
    $local_link = get_db_connection();
    
    $category = $_GET['category'] ?? '';
    $active_only = isset($_GET['active_only']) ? (bool)$_GET['active_only'] : true;
    
    $sql = "SELECT * FROM service_prices WHERE 1=1";
    $params = [];
    $types = "";
    
    if ($category) {
        $sql .= " AND service_category = ?";
        $params[] = $category;
        $types .= "s";
    }
    
    if ($active_only) {
        $sql .= " AND is_active = 1";
    }
    
    $sql .= " ORDER BY service_category, sort_order, item_name";
    
    if (empty($params)) {
        $result = mysqli_query($local_link, $sql);
        if ($result) {
            $prices = mysqli_fetch_all($result, MYSQLI_ASSOC);
            echo json_encode(['success' => true, 'prices' => $prices]);
        } else {
            echo json_encode(['success' => false, 'message' => 'Failed to fetch prices']);
        }
    } else {
        $stmt = execute_query($local_link, $sql, $types, $params);
        if ($stmt) {
            $result = mysqli_stmt_get_result($stmt);
            $prices = mysqli_fetch_all($result, MYSQLI_ASSOC);
            mysqli_stmt_close($stmt);
            echo json_encode(['success' => true, 'prices' => $prices]);
        } else {
            echo json_encode(['success' => false, 'message' => 'Failed to fetch prices']);
        }
    }
    
    close_db_connection($local_link);
}

/**
 * Get single price by ID
 */
function getPrice() {
    // Create a new dedicated connection for this operation
    $local_link = get_db_connection();
    
    $id = $_GET['id'] ?? 0;
    if (!$id) {
        echo json_encode(['success' => false, 'message' => 'Price ID required']);
        close_db_connection($local_link);
        return;
    }
    
    $sql = "SELECT * FROM service_prices WHERE id = ?";
    $stmt = execute_query($local_link, $sql, "i", [$id]);
    
    if ($stmt) {
        $result = mysqli_stmt_get_result($stmt);
        $price = mysqli_fetch_assoc($result);
        mysqli_stmt_close($stmt);
        
        if ($price) {
            echo json_encode(['success' => true, 'price' => $price]);
        } else {
            echo json_encode(['success' => false, 'message' => 'Price not found']);
        }
    } else {
        echo json_encode(['success' => false, 'message' => 'Failed to fetch price']);
    }
    
    close_db_connection($local_link);
}

/**
 * Update existing price
 */
function updatePrice() {
    // Create a new dedicated connection for this operation
    $local_link = get_db_connection();
    
    $id = $_POST['id'] ?? 0;
    $item_name = $_POST['item_name'] ?? '';
    $item_name_en = $_POST['item_name_en'] ?? '';
    $item_name_zh = $_POST['item_name_zh'] ?? '';
    $base_price = $_POST['base_price'] ?? 0;
    $unit = $_POST['unit'] ?? 'each';
    $description = $_POST['description'] ?? '';
    $description_en = $_POST['description_en'] ?? '';
    $description_zh = $_POST['description_zh'] ?? '';
    $is_active = isset($_POST['is_active']) ? (bool)$_POST['is_active'] : true;
    $sort_order = $_POST['sort_order'] ?? 0;
    
    if (!$id || !$item_name || $base_price < 0) {
        echo json_encode(['success' => false, 'message' => 'Invalid input data']);
        close_db_connection($local_link);
        return;
    }
    
    $sql = "UPDATE service_prices SET
            item_name = ?, item_name_en = ?, item_name_zh = ?,
            base_price = ?, unit = ?,
            description = ?, description_en = ?, description_zh = ?,
            is_active = ?, sort_order = ?
            WHERE id = ?";
    
    $stmt = execute_query($local_link, $sql, "sssdssssiis", [
        $item_name, $item_name_en, $item_name_zh,
        $base_price, $unit,
        $description, $description_en, $description_zh,
        $is_active, $sort_order, $id
    ]);
    
    if ($stmt) {
        mysqli_stmt_close($stmt);
        echo json_encode(['success' => true, 'message' => 'Price updated successfully']);
    } else {
        echo json_encode(['success' => false, 'message' => 'Failed to update price']);
    }
    
    close_db_connection($local_link);
}

/**
 * Add new price
 */
function addPrice() {
    // Create a new dedicated connection for this operation
    $local_link = get_db_connection();
    
    $service_category = $_POST['service_category'] ?? '';
    $service_type = $_POST['service_type'] ?? '';
    $item_name = $_POST['item_name'] ?? '';
    $item_name_en = $_POST['item_name_en'] ?? '';
    $item_name_zh = $_POST['item_name_zh'] ?? '';
    $base_price = $_POST['base_price'] ?? 0;
    $unit = $_POST['unit'] ?? 'each';
    $description = $_POST['description'] ?? '';
    $description_en = $_POST['description_en'] ?? '';
    $description_zh = $_POST['description_zh'] ?? '';
    $is_active = isset($_POST['is_active']) ? (bool)$_POST['is_active'] : true;
    $sort_order = $_POST['sort_order'] ?? 0;
    
    if (!$service_category || !$service_type || !$item_name || $base_price < 0) {
        echo json_encode(['success' => false, 'message' => 'Invalid input data']);
        close_db_connection($local_link);
        return;
    }
    
    $sql = "INSERT INTO service_prices
            (service_category, service_type, item_name, item_name_en, item_name_zh,
             base_price, unit, description, description_en, description_zh,
             is_active, sort_order)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
    
    $stmt = execute_query($local_link, $sql, "sssssdssssii", [
        $service_category, $service_type, $item_name, $item_name_en, $item_name_zh,
        $base_price, $unit, $description, $description_en, $description_zh,
        $is_active, $sort_order
    ]);
    
    if ($stmt) {
        $new_id = mysqli_insert_id($local_link);
        mysqli_stmt_close($stmt);
        echo json_encode(['success' => true, 'message' => 'Price added successfully', 'id' => $new_id]);
    } else {
        echo json_encode(['success' => false, 'message' => 'Failed to add price']);
    }
    
    close_db_connection($local_link);
}

/**
 * Delete price
 */
function deletePrice() {
    // Create a new dedicated connection for this operation
    $local_link = get_db_connection();
    
    $id = $_POST['id'] ?? 0;
    if (!$id) {
        echo json_encode(['success' => false, 'message' => 'Price ID required']);
        close_db_connection($local_link);
        return;
    }
    
    $sql = "DELETE FROM service_prices WHERE id = ?";
    $stmt = execute_query($local_link, $sql, "i", [$id]);
    
    if ($stmt) {
        mysqli_stmt_close($stmt);
        echo json_encode(['success' => true, 'message' => 'Price deleted successfully']);
    } else {
        echo json_encode(['success' => false, 'message' => 'Failed to delete price']);
    }
    
    close_db_connection($local_link);
}

/**
 * Toggle active status
 */
function toggleActive() {
    // Create a new dedicated connection for this operation
    $local_link = get_db_connection();
    
    $id = $_POST['id'] ?? 0;
    if (!$id) {
        echo json_encode(['success' => false, 'message' => 'Price ID required']);
        close_db_connection($local_link);
        return;
    }
    
    $sql = "UPDATE service_prices SET is_active = NOT is_active WHERE id = ?";
    $stmt = execute_query($local_link, $sql, "i", [$id]);
    
    if ($stmt) {
        mysqli_stmt_close($stmt);
        echo json_encode(['success' => true, 'message' => 'Status updated successfully']);
    } else {
        echo json_encode(['success' => false, 'message' => 'Failed to update status']);
    }
    
    close_db_connection($local_link);
}

/**
 * Update sort orders for multiple prices
 */
function updateSortOrders() {
    // Create a new dedicated connection for this operation
    $local_link = get_db_connection();
    
    $updates = $_POST['updates'] ?? '';
    if (!$updates) {
        echo json_encode(['success' => false, 'message' => 'Updates data required']);
        close_db_connection($local_link);
        return;
    }
    
    $updates_array = json_decode($updates, true);
    if (!is_array($updates_array)) {
        echo json_encode(['success' => false, 'message' => 'Invalid updates format']);
        close_db_connection($local_link);
        return;
    }
    
    // Start transaction
    mysqli_begin_transaction($local_link);
    
    try {
        foreach ($updates_array as $update) {
            $id = $update['id'] ?? 0;
            $sort_order = $update['sort_order'] ?? 0;
            
            if (!$id) {
                throw new Exception('Invalid ID in updates');
            }
            
            $sql = "UPDATE service_prices SET sort_order = ? WHERE id = ?";
            $stmt = execute_query($local_link, $sql, "ii", [$sort_order, $id]);
            
            if (!$stmt) {
                throw new Exception('Failed to update sort order for ID: ' . $id);
            }
            
            mysqli_stmt_close($stmt);
        }
        
        // Commit transaction
        mysqli_commit($local_link);
        echo json_encode(['success' => true, 'message' => 'Sort orders updated successfully']);
        
    } catch (Exception $e) {
        // Rollback transaction on error
        mysqli_rollback($local_link);
        echo json_encode(['success' => false, 'message' => $e->getMessage()]);
    }
    
    // Close the dedicated connection
    close_db_connection($local_link);
}
?>
