<?php
/**
 * KMS 系統重整後驗證測試
 * 測試所有主要功能是否正常運作
 */

echo "<h1>KMS 系統重整後驗證測試</h1>";

// 測試 1: 檢查主要文件是否存在
echo "<h2>1. 檢查主要文件結構</h2>";

$required_files = [
    // Core files
    'index.php',
    'KMS_Apps/KMS_Core/KMS_Config/KMS_PHP/KMS_config.php',
    'KMS_Apps/KMS_Core/KMS_Functions/KMS_PHP/KMS_functions.php',
    'KMS_Apps/KMS_Core/KMS_Language/KMS_PHP/KMS_language.php',
    
    // Index area
    'KMS_Apps/KMS_Index/KMS_Homepage/KMS_PHP/KMS_index.php',
    'KMS_Apps/KMS_Index/KMS_Authentication/KMS_PHP/KMS_login.php',
    'KMS_Apps/KMS_Index/KMS_Authentication/KMS_PHP/KMS_register.php',
    
    // Member area
    'KMS_Apps/KMS_Member/KMS_Dashboard/KMS_PHP/KMS_member.php',
    'KMS_Apps/KMS_Member/KMS_Wallet/KMS_PHP/KMS_credit_system.php',
    'KMS_Apps/KMS_Member/KMS_PCBuilder/KMS_PHP/KMS_pc_components_api.php',
    
    // Admin area
    'KMS_Apps/KMS_Admin/KMS_Dashboard/KMS_PHP/KMS_admin.php',
    'KMS_Apps/KMS_Admin/KMS_CreditManagement/KMS_PHP/KMS_admin_credit_dashboard.php',
    'KMS_Apps/KMS_Admin/KMS_OrderManagement/KMS_PHP/KMS_admin_orders.php',
    
    // Database
    'SQL/complete_database_setup.sql'
];

$missing_files = [];
foreach ($required_files as $file) {
    if (file_exists($file)) {
        echo "✅ {$file}<br>";
    } else {
        echo "❌ {$file}<br>";
        $missing_files[] = $file;
    }
}

if (empty($missing_files)) {
    echo "<p style='color: green;'>✅ 所有主要文件都存在！</p>";
} else {
    echo "<p style='color: red;'>❌ 缺少 " . count($missing_files) . " 個文件</p>";
}

// 測試 2: 檢查目錄結構
echo "<h2>2. 檢查目錄結構</h2>";

$required_dirs = [
    'KMS_Apps/KMS_Core',
    'KMS_Apps/KMS_Index',
    'KMS_Apps/KMS_Member', 
    'KMS_Apps/KMS_Admin',
    'KMS_Apps/KMS_Index/KMS_Authentication',
    'KMS_Apps/KMS_Index/KMS_Homepage',
    'KMS_Apps/KMS_Index/KMS_LiveChat',
    'KMS_Apps/KMS_Member/KMS_Dashboard',
    'KMS_Apps/KMS_Member/KMS_Wallet',
    'KMS_Apps/KMS_Member/KMS_PCBuilder',
    'KMS_Apps/KMS_Admin/KMS_Dashboard',
    'KMS_Apps/KMS_Admin/KMS_CreditManagement',
    'KMS_Apps/KMS_Admin/KMS_OrderManagement'
];

$missing_dirs = [];
foreach ($required_dirs as $dir) {
    if (is_dir($dir)) {
        echo "✅ {$dir}/<br>";
    } else {
        echo "❌ {$dir}/<br>";
        $missing_dirs[] = $dir;
    }
}

if (empty($missing_dirs)) {
    echo "<p style='color: green;'>✅ 所有目錄結構都正確！</p>";
} else {
    echo "<p style='color: red;'>❌ 缺少 " . count($missing_dirs) . " 個目錄</p>";
}

// 測試 3: 檢查 PHP 語法
echo "<h2>3. 檢查 PHP 語法</h2>";

$php_files_to_check = [
    'KMS_Apps/KMS_Index/KMS_Homepage/KMS_PHP/KMS_index.php',
    'KMS_Apps/KMS_Core/KMS_Config/KMS_PHP/KMS_config.php'
];

foreach ($php_files_to_check as $file) {
    if (file_exists($file)) {
        $output = [];
        $return_var = 0;
        exec("php -l \"$file\" 2>&1", $output, $return_var);
        
        if ($return_var === 0) {
            echo "✅ {$file} - 語法正確<br>";
        } else {
            echo "❌ {$file} - 語法錯誤: " . implode(' ', $output) . "<br>";
        }
    }
}

// 測試 4: 檢查數據庫文件
echo "<h2>4. 檢查數據庫文件</h2>";

if (file_exists('SQL/complete_database_setup.sql')) {
    $sql_content = file_get_contents('SQL/complete_database_setup.sql');
    
    // 檢查是否包含所有必要的表
    $required_tables = [
        'users',
        'user_wallets', 
        'credit_transactions',
        'orders',
        'affiliate_codes',
        'affiliate_commissions',
        'chat_sessions',
        'chat_messages',
        'pc_component_categories',
        'pc_components',
        'pc_prebuilt_configs',
        'pc_orders',
        'service_prices'
    ];
    
    $missing_tables = [];
    foreach ($required_tables as $table) {
        if (strpos($sql_content, "CREATE TABLE IF NOT EXISTS $table") !== false) {
            echo "✅ 表 {$table}<br>";
        } else {
            echo "❌ 表 {$table}<br>";
            $missing_tables[] = $table;
        }
    }
    
    if (empty($missing_tables)) {
        echo "<p style='color: green;'>✅ 所有數據表結構都已整合！</p>";
    } else {
        echo "<p style='color: red;'>❌ 缺少 " . count($missing_tables) . " 個表結構</p>";
    }
} else {
    echo "<p style='color: red;'>❌ 數據庫設置文件不存在</p>";
}

// 測試 5: 檢查是否清理了測試文件
echo "<h2>5. 檢查測試文件清理</h2>";

$test_patterns = [
    'test_*.php',
    '*_test.html',
    'debug_*.php',
    'check_*.php',
    'setup_*.php',
    'fix_*.php'
];

$found_test_files = [];
foreach (glob('PHP/*') as $file) {
    $basename = basename($file);
    foreach ($test_patterns as $pattern) {
        if (fnmatch($pattern, $basename)) {
            $found_test_files[] = $file;
            break;
        }
    }
}

if (empty($found_test_files)) {
    echo "<p style='color: green;'>✅ 測試文件已成功清理！</p>";
} else {
    echo "<p style='color: orange;'>⚠️ 仍有一些測試文件存在:</p>";
    foreach ($found_test_files as $file) {
        echo "- {$file}<br>";
    }
}

// 總結
echo "<h2>總結</h2>";
$total_issues = count($missing_files) + count($missing_dirs) + count($missing_tables);

if ($total_issues === 0) {
    echo "<p style='color: green; font-size: 18px; font-weight: bold;'>🎉 系統重整成功！所有功能結構完整。</p>";
    echo "<p>✅ 模組化結構已建立<br>";
    echo "✅ 所有文件已正確移動<br>";
    echo "✅ 數據庫結構已整合<br>";
    echo "✅ 測試文件已清理<br>";
    echo "✅ 系統準備就緒</p>";
} else {
    echo "<p style='color: red; font-size: 18px; font-weight: bold;'>⚠️ 系統重整需要修正 {$total_issues} 個問題</p>";
}

echo "<hr>";
echo "<p><strong>下一步建議:</strong></p>";
echo "<ol>";
echo "<li>運行數據庫設置: 訪問並執行 SQL/complete_database_setup.sql</li>";
echo "<li>測試首頁: 訪問 index.php</li>";
echo "<li>測試會員功能: 註冊新用戶並登入</li>";
echo "<li>測試管理功能: 使用管理員帳戶登入</li>";
echo "<li>測試 PC 建構器功能</li>";
echo "</ol>";
?>
