<?php
/**
 * KMS Credit Security Functions
 * Handles security measures for credit system including fraud detection, rate limiting, etc.
 */

require_once 'config.php';

class CreditSecurity {
    private $db;
    
    public function __construct($database_connection) {
        $this->db = $database_connection;
    }
    
    /**
     * Check if user has exceeded daily transaction limits
     */
    public function checkDailyLimits($user_id, $amount, $transaction_type) {
        $today = date('Y-m-d');
        
        // Get daily limits from settings
        $limits = $this->getDailyLimits();
        
        // Check daily deposit limit
        if ($transaction_type === 'deposit') {
            $sql = "SELECT SUM(amount) as daily_total FROM credit_transactions 
                    WHERE user_id = ? AND transaction_type = 'deposit' 
                    AND DATE(created_at) = ? AND status = 'completed'";
            $stmt = execute_query($this->db, $sql, "is", [$user_id, $today]);
            
            if ($stmt) {
                $result = mysqli_stmt_get_result($stmt);
                $row = mysqli_fetch_assoc($result);
                $daily_total = $row['daily_total'] ?? 0;
                mysqli_stmt_close($stmt);
                
                if (($daily_total + $amount) > $limits['daily_deposit_limit']) {
                    return [
                        'allowed' => false,
                        'message' => 'Daily deposit limit exceeded. Limit: $' . number_format($limits['daily_deposit_limit'], 2)
                    ];
                }
            }
        }
        
        // Check daily transfer limit
        if ($transaction_type === 'transfer') {
            $sql = "SELECT SUM(amount) as daily_total FROM credit_transactions 
                    WHERE user_id = ? AND transaction_type = 'transfer_out' 
                    AND DATE(created_at) = ? AND status = 'completed'";
            $stmt = execute_query($this->db, $sql, "is", [$user_id, $today]);
            
            if ($stmt) {
                $result = mysqli_stmt_get_result($stmt);
                $row = mysqli_fetch_assoc($result);
                $daily_total = $row['daily_total'] ?? 0;
                mysqli_stmt_close($stmt);
                
                if (($daily_total + $amount) > $limits['daily_transfer_limit']) {
                    return [
                        'allowed' => false,
                        'message' => 'Daily transfer limit exceeded. Limit: $' . number_format($limits['daily_transfer_limit'], 2)
                    ];
                }
            }
        }
        
        return ['allowed' => true];
    }
    
    /**
     * Check for suspicious activity patterns
     */
    public function checkSuspiciousActivity($user_id, $amount, $transaction_type) {
        $suspicious_flags = [];
        
        // Check for rapid successive transactions
        $sql = "SELECT COUNT(*) as recent_count FROM credit_transactions 
                WHERE user_id = ? AND created_at >= DATE_SUB(NOW(), INTERVAL 5 MINUTE)";
        $stmt = execute_query($this->db, $sql, "i", [$user_id]);
        
        if ($stmt) {
            $result = mysqli_stmt_get_result($stmt);
            $row = mysqli_fetch_assoc($result);
            $recent_count = $row['recent_count'] ?? 0;
            mysqli_stmt_close($stmt);
            
            if ($recent_count >= 5) {
                $suspicious_flags[] = 'Too many transactions in short time';
            }
        }
        
        // Check for unusually large amounts
        $sql = "SELECT AVG(amount) as avg_amount FROM credit_transactions 
                WHERE user_id = ? AND transaction_type = ? AND status = 'completed' 
                AND created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)";
        $stmt = execute_query($this->db, $sql, "is", [$user_id, $transaction_type]);
        
        if ($stmt) {
            $result = mysqli_stmt_get_result($stmt);
            $row = mysqli_fetch_assoc($result);
            $avg_amount = $row['avg_amount'] ?? 0;
            mysqli_stmt_close($stmt);
            
            if ($avg_amount > 0 && $amount > ($avg_amount * 5)) {
                $suspicious_flags[] = 'Amount significantly higher than usual';
            }
        }
        
        // Check for new user with large transaction
        $sql = "SELECT created_at FROM users WHERE id = ?";
        $stmt = execute_query($this->db, $sql, "i", [$user_id]);
        
        if ($stmt) {
            $result = mysqli_stmt_get_result($stmt);
            $row = mysqli_fetch_assoc($result);
            mysqli_stmt_close($stmt);
            
            if ($row) {
                $account_age = time() - strtotime($row['created_at']);
                if ($account_age < (7 * 24 * 60 * 60) && $amount > 100) { // Less than 7 days old
                    $suspicious_flags[] = 'New account with large transaction';
                }
            }
        }
        
        return [
            'is_suspicious' => !empty($suspicious_flags),
            'flags' => $suspicious_flags,
            'requires_review' => count($suspicious_flags) >= 2
        ];
    }
    
    /**
     * Rate limiting for API requests
     */
    public function checkRateLimit($user_id, $action) {
        $rate_limits = [
            'deposit' => ['limit' => 5, 'window' => 3600], // 5 deposits per hour
            'transfer' => ['limit' => 10, 'window' => 3600], // 10 transfers per hour
            'wallet_check' => ['limit' => 100, 'window' => 3600] // 100 wallet checks per hour
        ];
        
        if (!isset($rate_limits[$action])) {
            return ['allowed' => true];
        }
        
        $limit = $rate_limits[$action]['limit'];
        $window = $rate_limits[$action]['window'];
        
        // Create rate limit table if not exists
        $this->createRateLimitTable();
        
        // Clean old entries
        $sql = "DELETE FROM rate_limits WHERE created_at < DATE_SUB(NOW(), INTERVAL ? SECOND)";
        $stmt = execute_query($this->db, $sql, "i", [$window]);
        if ($stmt) mysqli_stmt_close($stmt);
        
        // Check current count
        $sql = "SELECT COUNT(*) as count FROM rate_limits 
                WHERE user_id = ? AND action = ? AND created_at >= DATE_SUB(NOW(), INTERVAL ? SECOND)";
        $stmt = execute_query($this->db, $sql, "isi", [$user_id, $action, $window]);
        
        if ($stmt) {
            $result = mysqli_stmt_get_result($stmt);
            $row = mysqli_fetch_assoc($result);
            $current_count = $row['count'] ?? 0;
            mysqli_stmt_close($stmt);
            
            if ($current_count >= $limit) {
                return [
                    'allowed' => false,
                    'message' => 'Rate limit exceeded. Please try again later.',
                    'retry_after' => $window
                ];
            }
            
            // Record this request
            $sql = "INSERT INTO rate_limits (user_id, action, ip_address) VALUES (?, ?, ?)";
            $ip_address = $_SERVER['REMOTE_ADDR'] ?? 'unknown';
            $stmt = execute_query($this->db, $sql, "iss", [$user_id, $action, $ip_address]);
            if ($stmt) mysqli_stmt_close($stmt);
        }
        
        return ['allowed' => true];
    }
    
    /**
     * Validate transaction security token
     */
    public function validateSecurityToken($user_id, $token, $action) {
        // In a real implementation, you might use:
        // - CSRF tokens
        // - Time-based one-time passwords (TOTP)
        // - SMS verification codes
        // - Email verification codes
        
        // For demo, we'll use a simple session-based token
        $session_key = "security_token_{$action}_{$user_id}";
        
        if (!isset($_SESSION[$session_key])) {
            return ['valid' => false, 'message' => 'Security token not found'];
        }
        
        $stored_token = $_SESSION[$session_key];
        
        if ($stored_token['token'] !== $token) {
            return ['valid' => false, 'message' => 'Invalid security token'];
        }
        
        if (time() > $stored_token['expires']) {
            unset($_SESSION[$session_key]);
            return ['valid' => false, 'message' => 'Security token expired'];
        }
        
        // Token is valid, remove it (one-time use)
        unset($_SESSION[$session_key]);
        
        return ['valid' => true];
    }
    
    /**
     * Generate security token for sensitive operations
     */
    public function generateSecurityToken($user_id, $action, $expires_in = 300) {
        $token = bin2hex(random_bytes(16));
        $expires = time() + $expires_in;
        
        $session_key = "security_token_{$action}_{$user_id}";
        $_SESSION[$session_key] = [
            'token' => $token,
            'expires' => $expires,
            'action' => $action,
            'user_id' => $user_id
        ];
        
        return [
            'token' => $token,
            'expires' => $expires,
            'expires_in' => $expires_in
        ];
    }
    
    /**
     * Log security events
     */
    public function logSecurityEvent($user_id, $event_type, $details, $severity = 'info') {
        $this->createSecurityLogTable();
        
        $sql = "INSERT INTO security_logs (user_id, event_type, details, severity, ip_address, user_agent) 
                VALUES (?, ?, ?, ?, ?, ?)";
        
        $ip_address = $_SERVER['REMOTE_ADDR'] ?? 'unknown';
        $user_agent = $_SERVER['HTTP_USER_AGENT'] ?? 'unknown';
        $details_json = is_array($details) ? json_encode($details) : $details;
        
        $stmt = execute_query($this->db, $sql, "isssss", [
            $user_id, $event_type, $details_json, $severity, $ip_address, $user_agent
        ]);
        
        if ($stmt) {
            mysqli_stmt_close($stmt);
        }
    }
    
    /**
     * Get daily limits from settings
     */
    private function getDailyLimits() {
        $defaults = [
            'daily_deposit_limit' => 10000.00,
            'daily_transfer_limit' => 5000.00,
            'daily_withdrawal_limit' => 2000.00
        ];
        
        $sql = "SELECT setting_key, setting_value FROM credit_system_settings 
                WHERE setting_key IN ('daily_deposit_limit', 'daily_transfer_limit', 'daily_withdrawal_limit')";
        $result = mysqli_query($this->db, $sql);
        
        if ($result) {
            while ($row = mysqli_fetch_assoc($result)) {
                $defaults[$row['setting_key']] = (float)$row['setting_value'];
            }
        }
        
        return $defaults;
    }
    
    /**
     * Create rate limit table if not exists
     */
    private function createRateLimitTable() {
        $sql = "CREATE TABLE IF NOT EXISTS rate_limits (
            id INT AUTO_INCREMENT PRIMARY KEY,
            user_id INT NOT NULL,
            action VARCHAR(50) NOT NULL,
            ip_address VARCHAR(45),
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            INDEX idx_user_action (user_id, action),
            INDEX idx_created_at (created_at)
        )";
        
        mysqli_query($this->db, $sql);
    }
    
    /**
     * Create security log table if not exists
     */
    private function createSecurityLogTable() {
        $sql = "CREATE TABLE IF NOT EXISTS security_logs (
            id INT AUTO_INCREMENT PRIMARY KEY,
            user_id INT,
            event_type VARCHAR(100) NOT NULL,
            details TEXT,
            severity ENUM('info', 'warning', 'error', 'critical') DEFAULT 'info',
            ip_address VARCHAR(45),
            user_agent TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            INDEX idx_user_id (user_id),
            INDEX idx_event_type (event_type),
            INDEX idx_severity (severity),
            INDEX idx_created_at (created_at)
        )";
        
        mysqli_query($this->db, $sql);
    }
}

// Initialize security system
$credit_security = new CreditSecurity($link);
?>
