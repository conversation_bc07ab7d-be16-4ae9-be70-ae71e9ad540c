/* Custom Modal Styles for Beautiful Notifications */
.custom-modal {
    display: none;
    position: fixed;
    z-index: 2000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.8);
    animation: fadeIn 0.3s ease-in-out;
}

.custom-modal-content {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    margin: 15% auto;
    padding: 0;
    border: none;
    width: 90%;
    max-width: 700px;
    border-radius: 20px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.5);
    animation: slideIn 0.4s ease-out;
    overflow: hidden;
}

.custom-modal-header {
    padding: 25px 30px 15px;
    text-align: center;
    color: white;
}

.custom-modal-header h3 {
    margin: 0;
    font-size: 24px;
    font-weight: 600;
}

.custom-modal-body {
    padding: 15px 30px 30px;
    text-align: center;
    color: white;
}

.custom-modal-body p {
    margin: 0;
    font-size: 16px;
    line-height: 1.5;
}

.custom-modal-footer {
    padding: 0 30px 30px;
    text-align: center;
}

.custom-modal-btn {
    background: rgba(255, 255, 255, 0.2);
    border: 2px solid rgba(255, 255, 255, 0.3);
    color: white;
    padding: 12px 30px;
    border-radius: 25px;
    cursor: pointer;
    font-size: 16px;
    font-weight: 500;
    transition: all 0.3s ease;
    margin: 0 5px;
}

.custom-modal-btn:hover {
    background: rgba(255, 255, 255, 0.3);
    border-color: rgba(255, 255, 255, 0.5);
    transform: translateY(-2px);
}

.custom-modal-btn.primary {
    background: #4CAF50;
    border-color: #4CAF50;
}

.custom-modal-btn.primary:hover {
    background: #45a049;
    border-color: #45a049;
}

.custom-modal-btn.danger {
    background: #f44336;
    border-color: #f44336;
}

.custom-modal-btn.danger:hover {
    background: #da190b;
    border-color: #da190b;
}

.custom-modal-btn.warning {
    background: #ff9800;
    border-color: #ff9800;
}

.custom-modal-btn.warning:hover {
    background: #e68900;
    border-color: #e68900;
}

/* Success Modal */
.custom-modal.success .custom-modal-content {
    background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
}

/* Error Modal */
.custom-modal.error .custom-modal-content {
    background: linear-gradient(135deg, #f44336 0%, #da190b 100%);
}

/* Warning Modal */
.custom-modal.warning .custom-modal-content {
    background: linear-gradient(135deg, #ff9800 0%, #e68900 100%);
}

/* Info Modal */
.custom-modal.info .custom-modal-content {
    background: linear-gradient(135deg, #2196F3 0%, #1976D2 100%);
}

/* Confirmation Modal */
.custom-modal.confirm .custom-modal-content {
    background: linear-gradient(135deg, #9C27B0 0%, #7B1FA2 100%);
}

/* Animations */
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideIn {
    from {
        transform: translateY(-50px);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

/* Close button */
.custom-modal-close {
    position: absolute;
    top: 15px;
    right: 20px;
    color: rgba(255, 255, 255, 0.7);
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
    transition: color 0.3s ease;
}

.custom-modal-close:hover {
    color: white;
}

/* Order Status Styles */
.order-status {
    display: inline-block;
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 18px;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.order-status.pending {
    background: #989329;
    color: #ffbf00;
    border: 1px solid #ffeaa7;
}

.order-status.processing {
    background: #2a6771;
    color: #09c1e2;
    border: 1px solid #bee5eb;
}

.order-status.completed {
    background: #428652;
    color: #06e83b;
    border: 1px solid #c3e6cb;
}

.order-status.cancelled {
    background: #e67d86;
    color: #e91227;
    border: 1px solid #f5c6cb;
}

/* Order Card Styles */
.order-card {
    background: #1eada3;
    border-radius: 14px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    color: #ffffff;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.order-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
}

.order-card h4 {
    margin-top: 0;
    color: #ffffff;
    font-size: 24px;
}

.order-actions {
    margin-top: 15px;
    text-align: right;
}

.order-actions button {
    background: #e74c3c;
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 20px;
    cursor: pointer;
    font-size: 14px;
    transition: background 0.3s ease;
}

.order-actions button:hover {
    background: #c0392b;
}

.order-actions button:disabled {
    background: #bdc3c7;
    cursor: not-allowed;
}

/* Loading spinner */
.loading-spinner {
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top: 3px solid white;
    width: 30px;
    height: 30px;
    animation: spin 1s linear infinite;
    margin: 10px auto;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Add text-shadow to all text on registration page */
#registerModal * {
    text-shadow: 2px 2px 4px black;
}

/* Custom Date Picker Styles */
.custom-date-picker {
    display: flex;
    gap: 10px;
    justify-content: center;
    margin: 10px 15px;
}
.custom-date-picker select {
    padding: 10px 40px 10px 15px;
    width: 100px;
    font-size: 16px;
    border: none;
    border-radius: 10px;
    background: linear-gradient(135deg, #ffffff, #f0f0f0);
    color: #333;
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    appearance: none;
    background-image: url("data:image/svg+xml;utf8,<svg fill='%23666' height='20' viewBox='0 0 24 24' width='20' xmlns='http://www.w3.org/2000/svg'><path d='M7 10l5 5 5-5z'/></svg>");
    background-repeat: no-repeat;
/* Fancy Birthday Picker Styling */
.date-picker {
    width: 100%;
    max-width: 600px;
    margin: 10px auto;
    padding: 12px 40px 12px 16px;
    font-size: 18px;
    border: none;
    border-radius: 50px;
    background: linear-gradient(135deg, #ffffff, #f0f0f5);
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    background-image: url("data:image/svg+xml;utf8,<svg fill='%23666' height='24' viewBox='0 0 24 24' width='24' xmlns='http://www.w3.org/2000/svg'><path d='M19 4h-1V2h-2v2H8V2H6v2H5C3.9 4 3 .9 3 2v16c0 1.1 .9 2 2 2h14c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2zm0 18H5V9h14v13zm-7-8l5 3-5 3v-6z'/></svg>");
    background-repeat: no-repeat;
    background-position: right 16px center;
    background-size: 24px;
    cursor: pointer;
}
.flatpickr-calendar {
    width: 600px !important;
    max-width: 600px !important;
    border: none;
    box-shadow: 0 10px 30px rgba(0,0,0,0.15);
    border-radius: 12px;
    overflow: hidden;
}
.flatpickr-current-month {
    background: linear-gradient(135deg, #667eea, #764ba2);
    padding: 8px 0;
    color: white;
    font-weight: 600;
}
.flatpickr-weekdays {
    background: #f5f5f5;
}
.flatpickr-day {
    color: #333;
}
.flatpickr-day.today {
    background: #667eea;
    color: white;
}
.flatpickr-day.selected {
    background: #764ba2;
    color: white;
}
    background-position: right 10px center;
    background-size: 12px;
    transition: box-shadow 0.3s ease;
    cursor: pointer;
}

/* Enhance Flatpickr Year & Month Dropdowns */
#registerModal .flatpickr-current-month .flatpickr-monthDropdown-months,
#registerModal .flatpickr-current-month .numInputWrapper input {
    width: 5em;
    padding: 0.4em 0.6em;
    font-size: 1em;
    border-radius: 6px;
    border: 1px solid rgba(0,0,0,0.1);
}
/* Limit calendar inset size for better visibility */
#registerModal .flatpickr-calendar {
    width: 600px;
    max-width: 600px;
}
/* Increase click target for dropdowns */
#registerModal .flatpickr-current-month .flatpickr-monthDropdown-months,
#registerModal .flatpickr-current-month .numInputWrapper {
    margin: 0 0.2em;
}

/* Custom styles for the birthday picker */
.birthday-picker-container {
    display: flex;
    justify-content: space-between;
    width: 100%;
    max-width: 600px; /* As requested */
    margin: 0 auto;
}

.birthday-select-group {
    display: flex;
    flex-direction: column;
    flex: 1;
    margin: 0 5px;
}

.birthday-select-group label {
    margin-top: 15px;
    margin-bottom: 10px;
    color: #ffffff;
    font-size: 24px;
    text-align: center;
}

.birthday-picker-container select {
    padding: 8px;
    border: 2px solid #ffa800;
    border-radius: 10px;
    background-color: #fda10f;
    color: #fff;
    font-size: 22px;
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23fff' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right 15px center;
    background-size: 1em;
    cursor: pointer;
    transition: all 0.3s ease;
    width: 100%;
}

.birthday-select-group:first-child {
    margin-left: 0;
}

.birthday-select-group:last-child {
    margin-right: 0;
}

.birthday-picker-container select:hover {
    border-color: #00f2ff;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.7);
}

.birthday-picker-container select:focus {
    outline: none;
    border-color: #00ffd0;
    box-shadow: 0 0 15px rgba(0, 0, 0, 0.7);
}

.birthday-picker-container select option {
    background-color: #ff8400;
    color: #fff;
}
