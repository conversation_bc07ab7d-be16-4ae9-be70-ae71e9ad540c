<?php
// --- Language Configuration & Functions ---

// Default language
$default_lang = 'en';

// Supported languages
$supported_langs = ['en', 'zh-CN'];

// --- Language Detection ---
$lang = $default_lang;

if (isset($_SESSION["loggedin"]) && isset($_SESSION["language"])) {
    // 1. Check for logged-in user's preference from session
    $user_lang = $_SESSION["language"];
    if (in_array($user_lang, $supported_langs)) {
        $lang = $user_lang;
    }
} elseif (isset($_COOKIE['lang'])) {
    // 2. Check for visitor's preference from cookie
    $cookie_lang = $_COOKIE['lang'];
    if (in_array($cookie_lang, $supported_langs)) {
        $lang = $cookie_lang;
    }
}

// --- Database-based Activity Tracking ---
// This part updates the 'last_seen' timestamp for the logged-in user.
// It relies on config.php being included before this file.
if (isset($_SESSION["loggedin"]) && $_SESSION["loggedin"] === true && isset($_SESSION['username'])) {
    // We can safely assume $link is available here.
    $username = $_SESSION['username'];
    $sql = "UPDATE users SET last_seen = NOW() WHERE username = ?";
    
    $stmt = mysqli_prepare($link, $sql);
    if ($stmt) {
        mysqli_stmt_bind_param($stmt, "s", $username);
        mysqli_stmt_execute($stmt);
        mysqli_stmt_close($stmt);
    }
}
 
// --- Load Language File ---
$lang_file_path = __DIR__ . '/../lang/' . $lang . '.json';
$translations = [];

if (file_exists($lang_file_path)) {
    $translations = json_decode(file_get_contents($lang_file_path), true);
} else {
    // Fallback to default language if the file is missing for some reason
    $default_lang_file_path = __DIR__ . '/../lang/' . $default_lang . '.json';
    if (file_exists($default_lang_file_path)) {
        $translations = json_decode(file_get_contents($default_lang_file_path), true);
    }
}

// --- Translation Function ---

/**
 * Gets a translated string for a given key.
 *
 * @param string $key The key for the translation string.
 * @return string The translated string, or the key itself if not found.
 */
function t($key) {
    global $translations;
    return isset($translations[$key]) ? $translations[$key] : $key;
}

?>