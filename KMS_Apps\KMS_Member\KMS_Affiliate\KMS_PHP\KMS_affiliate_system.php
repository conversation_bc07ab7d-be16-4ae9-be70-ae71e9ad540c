<?php
/**
 * <PERSON><PERSON> Affiliate System Core Functions
 * Handles affiliate referrals, commissions, and withdrawals
 */

$base_path = dirname(dirname(dirname(dirname(__DIR__))));
require_once $base_path . DIRECTORY_SEPARATOR . 'KMS_Apps' . DIRECTORY_SEPARATOR . 'KMS_Core' . DIRECTORY_SEPARATOR . 'KMS_Config' . DIRECTORY_SEPARATOR . 'KMS_PHP' . DIRECTORY_SEPARATOR . 'KMS_config.php';
require_once $base_path . DIRECTORY_SEPARATOR . 'KMS_Apps' . DIRECTORY_SEPARATOR . 'KMS_Core' . DIRECTORY_SEPARATOR . 'KMS_Functions' . DIRECTORY_SEPARATOR . 'KMS_PHP' . DIRECTORY_SEPARATOR . 'KMS_functions.php';
require_once $base_path . DIRECTORY_SEPARATOR . 'KMS_Apps' . DIRECTORY_SEPARATOR . 'KMS_Member' . DIRECTORY_SEPARATOR . 'KMS_Wallet' . DIRECTORY_SEPARATOR . 'KMS_PHP' . DIRECTORY_SEPARATOR . 'KMS_credit_system.php';

class AffiliateSystem {
    private $db;
    private $credit_system;
    
    public function __construct($database_connection, $credit_system) {
        $this->db = $database_connection;
        $this->credit_system = $credit_system;
    }
    
    /**
     * Get user's affiliate code
     */
    public function getUserAffiliateCode($user_id) {
        $sql = "SELECT * FROM affiliate_codes WHERE user_id = ?";
        $stmt = execute_query($this->db, $sql, "i", [$user_id]);
        
        if ($stmt) {
            $result = mysqli_stmt_get_result($stmt);
            $affiliate = mysqli_fetch_assoc($result);
            mysqli_stmt_close($stmt);
            
            if (!$affiliate) {
                // Create affiliate code if it doesn't exist
                if ($this->createAffiliateCode($user_id)) {
                    return $this->getUserAffiliateCode($user_id);
                } else {
                    return false;
                }
            }
            
            return $affiliate;
        }
        
        return false;
    }
    
    /**
     * Create affiliate code for user
     */
    private function createAffiliateCode($user_id) {
        // Get user info
        $sql = "SELECT username FROM users WHERE id = ?";
        $stmt = execute_query($this->db, $sql, "i", [$user_id]);
        
        if (!$stmt) return false;
        
        $result = mysqli_stmt_get_result($stmt);
        $user = mysqli_fetch_assoc($result);
        mysqli_stmt_close($stmt);
        
        if (!$user) return false;
        
        // Generate unique affiliate code
        $affiliate_code = $this->generateUniqueCode($user_id, $user['username']);
        
        // Insert affiliate code
        $sql = "INSERT INTO affiliate_codes (user_id, affiliate_code) VALUES (?, ?)";
        $stmt = execute_query($this->db, $sql, "is", [$user_id, $affiliate_code]);
        
        if ($stmt) {
            mysqli_stmt_close($stmt);
            return true;
        }
        
        return false;
    }
    
    /**
     * Generate unique affiliate code
     */
    private function generateUniqueCode($user_id, $username) {
        $attempts = 0;
        
        do {
            $code = 'KMS' . str_pad($user_id, 4, '0', STR_PAD_LEFT) . 
                    strtoupper(substr(md5($user_id . $username . time() . $attempts), 0, 4));
            
            $sql = "SELECT COUNT(*) as count FROM affiliate_codes WHERE affiliate_code = ?";
            $stmt = execute_query($this->db, $sql, "s", [$code]);
            
            if ($stmt) {
                $result = mysqli_stmt_get_result($stmt);
                $row = mysqli_fetch_assoc($result);
                $exists = $row['count'] > 0;
                mysqli_stmt_close($stmt);
                
                if (!$exists) {
                    return $code;
                }
            }
            
            $attempts++;
        } while ($attempts < 100);
        
        // Fallback if all attempts fail
        return 'KMS' . str_pad($user_id, 4, '0', STR_PAD_LEFT) . strtoupper(substr(uniqid(), -4));
    }
    
    /**
     * Track affiliate click/visit
     */
    public function trackAffiliateVisit($affiliate_code, $referrer_url = null, $landing_page = null) {
        $ip_address = $_SERVER['REMOTE_ADDR'] ?? 'unknown';
        $user_agent = $_SERVER['HTTP_USER_AGENT'] ?? 'unknown';
        
        // Check if affiliate code exists
        $sql = "SELECT user_id FROM affiliate_codes WHERE affiliate_code = ? AND is_active = TRUE";
        $stmt = execute_query($this->db, $sql, "s", [$affiliate_code]);
        
        if (!$stmt) return false;
        
        $result = mysqli_stmt_get_result($stmt);
        $affiliate = mysqli_fetch_assoc($result);
        mysqli_stmt_close($stmt);
        
        if (!$affiliate) return false;
        
        // Record the visit
        $sql = "INSERT INTO affiliate_tracking (affiliate_code, visitor_ip, user_agent, referrer_url, landing_page) 
                VALUES (?, ?, ?, ?, ?)";
        $stmt = execute_query($this->db, $sql, "sssss", [
            $affiliate_code, $ip_address, $user_agent, $referrer_url, $landing_page
        ]);
        
        if ($stmt) {
            mysqli_stmt_close($stmt);
            
            // Set cookie to track referral
            setcookie('kms_affiliate', $affiliate_code, time() + (30 * 24 * 60 * 60), '/'); // 30 days
            
            return true;
        }
        
        return false;
    }
    
    /**
     * Create referral relationship when user registers
     */
    public function createReferral($referred_user_id, $affiliate_code = null) {
        // If no affiliate code provided, check cookie
        if (!$affiliate_code && isset($_COOKIE['kms_affiliate'])) {
            $affiliate_code = $_COOKIE['kms_affiliate'];
        }
        
        if (!$affiliate_code) return false;
        
        // Get referrer info
        $sql = "SELECT user_id FROM affiliate_codes WHERE affiliate_code = ? AND is_active = TRUE";
        $stmt = execute_query($this->db, $sql, "s", [$affiliate_code]);
        
        if (!$stmt) return false;
        
        $result = mysqli_stmt_get_result($stmt);
        $affiliate = mysqli_fetch_assoc($result);
        mysqli_stmt_close($stmt);
        
        if (!$affiliate) return false;
        
        $referrer_id = $affiliate['user_id'];
        
        // Don't allow self-referral
        if ($referrer_id == $referred_user_id) return false;
        
        // Check if user is already referred
        $sql = "SELECT id FROM affiliate_referrals WHERE referred_id = ?";
        $stmt = execute_query($this->db, $sql, "i", [$referred_user_id]);
        
        if ($stmt) {
            $result = mysqli_stmt_get_result($stmt);
            $existing = mysqli_fetch_assoc($result);
            mysqli_stmt_close($stmt);
            
            if ($existing) return false; // Already referred
        }
        
        // Create referral relationship
        $ip_address = $_SERVER['REMOTE_ADDR'] ?? 'unknown';
        $user_agent = $_SERVER['HTTP_USER_AGENT'] ?? 'unknown';
        
        $sql = "INSERT INTO affiliate_referrals (referrer_id, referred_id, affiliate_code, ip_address, user_agent, status) 
                VALUES (?, ?, ?, ?, ?, 'pending')";
        $stmt = execute_query($this->db, $sql, "iisss", [
            $referrer_id, $referred_user_id, $affiliate_code, $ip_address, $user_agent
        ]);
        
        if ($stmt) {
            mysqli_stmt_close($stmt);
            
            // Update tracking record if exists
            $sql = "UPDATE affiliate_tracking SET converted = TRUE, converted_user_id = ? 
                    WHERE affiliate_code = ? AND visitor_ip = ? AND converted = FALSE 
                    ORDER BY created_at DESC LIMIT 1";
            $stmt = execute_query($this->db, $sql, "iss", [$referred_user_id, $affiliate_code, $ip_address]);
            if ($stmt) mysqli_stmt_close($stmt);
            
            // Clear the cookie
            setcookie('kms_affiliate', '', time() - 3600, '/');
            
            return true;
        }
        
        return false;
    }
    
    /**
     * Process commission when order is completed
     */
    public function processOrderCommission($order_id, $order_type = 'regular') {
        // Get order details
        if ($order_type === 'pc') {
            $sql = "SELECT user_id FROM pc_orders WHERE id = ?";
        } else {
            $sql = "SELECT user_id FROM orders WHERE id = ?";
        }
        
        $stmt = execute_query($this->db, $sql, "i", [$order_id]);
        
        if (!$stmt) return false;
        
        $result = mysqli_stmt_get_result($stmt);
        $order = mysqli_fetch_assoc($result);
        mysqli_stmt_close($stmt);
        
        if (!$order) return false;
        
        $customer_id = $order['user_id'];
        
        // Check if this customer was referred
        $sql = "SELECT referrer_id, affiliate_code FROM affiliate_referrals 
                WHERE referred_id = ? AND status = 'pending'";
        $stmt = execute_query($this->db, $sql, "i", [$customer_id]);
        
        if (!$stmt) return false;
        
        $result = mysqli_stmt_get_result($stmt);
        $referral = mysqli_fetch_assoc($result);
        mysqli_stmt_close($stmt);
        
        if (!$referral) return false; // No referral found
        
        $referrer_id = $referral['referrer_id'];
        
        // Check if commission already paid for this order
        $commission_check_sql = $order_type === 'pc' ? 
            "SELECT id FROM affiliate_commissions WHERE pc_order_id = ?" :
            "SELECT id FROM affiliate_commissions WHERE order_id = ?";
        
        $stmt = execute_query($this->db, $commission_check_sql, "i", [$order_id]);
        
        if ($stmt) {
            $result = mysqli_stmt_get_result($stmt);
            $existing_commission = mysqli_fetch_assoc($result);
            mysqli_stmt_close($stmt);
            
            if ($existing_commission) return false; // Commission already paid
        }
        
        mysqli_begin_transaction($this->db);
        
        try {
            // Get commission amount from settings
            $commission_amount = $this->getCommissionAmount();
            
            // Create commission record with 'pending_admin_review' status
            $commission_sql = $order_type === 'pc' ?
                "INSERT INTO affiliate_commissions (referrer_id, referred_id, pc_order_id, commission_amount, status, created_at) VALUES (?, ?, ?, ?, 'pending_admin_review', NOW())" :
                "INSERT INTO affiliate_commissions (referrer_id, referred_id, order_id, commission_amount, status, created_at) VALUES (?, ?, ?, ?, 'pending_admin_review', NOW())";

            $stmt = execute_query($this->db, $commission_sql, "iiid", [
                $referrer_id, $customer_id, $order_id, $commission_amount
            ]);

            if (!$stmt) {
                throw new Exception('Failed to create commission record');
            }

            $commission_id = mysqli_insert_id($this->db);
            mysqli_stmt_close($stmt);

            // Update referral status to 'pending_commission' (waiting for admin approval)
            $update_referral_sql = "UPDATE affiliate_referrals SET status = 'pending_commission', confirmed_at = NOW() WHERE referrer_id = ? AND referred_id = ?";
            $stmt = execute_query($this->db, $update_referral_sql, "ii", [$referrer_id, $customer_id]);
            if ($stmt) mysqli_stmt_close($stmt);
            
            // Update user wallet commission balance
            $update_wallet_sql = "UPDATE user_wallets SET commission_balance = commission_balance + ?, total_commissions = total_commissions + ? WHERE user_id = ?";
            $stmt = execute_query($this->db, $update_wallet_sql, "ddi", [$commission_amount, $commission_amount, $referrer_id]);
            if ($stmt) mysqli_stmt_close($stmt);
            
            mysqli_commit($this->db);
            
            return [
                'success' => true,
                'commission_id' => $commission_id,
                'amount' => $commission_amount,
                'referrer_id' => $referrer_id,
                'status' => 'pending_admin_review',
                'message' => 'Commission created and pending admin review'
            ];
            
        } catch (Exception $e) {
            mysqli_rollback($this->db);
            return ['success' => false, 'message' => $e->getMessage()];
        }
    }
    
    /**
     * Get commission amount from settings
     */
    private function getCommissionAmount() {
        $sql = "SELECT setting_value FROM affiliate_settings WHERE setting_key = 'commission_amount'";
        $result = mysqli_query($this->db, $sql);
        
        if ($result) {
            $row = mysqli_fetch_assoc($result);
            return $row ? (float)$row['setting_value'] : 50.00;
        }
        
        return 50.00; // Default commission
    }
    
    /**
     * Get affiliate statistics for user
     */
    public function getAffiliateStats($user_id) {
        $sql = "SELECT * FROM affiliate_stats WHERE user_id = ?";
        $stmt = execute_query($this->db, $sql, "i", [$user_id]);
        
        if ($stmt) {
            $result = mysqli_stmt_get_result($stmt);
            $stats = mysqli_fetch_assoc($result);
            mysqli_stmt_close($stmt);
            return $stats;
        }
        
        return false;
    }
    
    /**
     * Get referral history for user
     */
    public function getReferralHistory($user_id, $limit = 50, $offset = 0) {
        $sql = "SELECT ar.*, u.username as referred_username, u.email as referred_email,
                       ac.commission_amount, ac.status as commission_status, ac.paid_at
                FROM affiliate_referrals ar
                JOIN users u ON ar.referred_id = u.id
                LEFT JOIN affiliate_commissions ac ON ar.referrer_id = ac.referrer_id AND ar.referred_id = ac.referred_id
                WHERE ar.referrer_id = ?
                ORDER BY ar.created_at DESC
                LIMIT ? OFFSET ?";
        
        $stmt = execute_query($this->db, $sql, "iii", [$user_id, $limit, $offset]);
        
        if ($stmt) {
            $result = mysqli_stmt_get_result($stmt);
            $referrals = [];
            
            while ($row = mysqli_fetch_assoc($result)) {
                $referrals[] = $row;
            }
            
            mysqli_stmt_close($stmt);
            return $referrals;
        }
        
        return [];
    }

    /**
     * Admin approve commission
     */
    public function approveCommission($commission_id, $admin_user_id, $admin_notes = '') {
        mysqli_begin_transaction($this->db);

        try {
            // Get commission details
            $sql = "SELECT * FROM affiliate_commissions WHERE id = ? AND status = 'pending_admin_review'";
            $stmt = execute_query($this->db, $sql, "i", [$commission_id]);

            if (!$stmt) {
                throw new Exception('Failed to get commission details');
            }

            $result = mysqli_stmt_get_result($stmt);
            $commission = mysqli_fetch_assoc($result);
            mysqli_stmt_close($stmt);

            if (!$commission) {
                throw new Exception('Commission not found or already processed');
            }

            // Add commission to referrer's wallet
            $add_result = $this->credit_system->addCredit(
                $commission['referrer_id'],
                $commission['commission_amount'],
                'affiliate',
                "Affiliate commission approved for order #" . ($commission['order_id'] ?? $commission['pc_order_id']),
                "COMMISSION_$commission_id"
            );

            if (!$add_result['success']) {
                throw new Exception($add_result['message']);
            }

            // Update commission record
            $update_sql = "UPDATE affiliate_commissions SET
                          status = 'approved',
                          transaction_id = ?,
                          approved_by = ?,
                          admin_notes = ?,
                          approved_at = NOW()
                          WHERE id = ?";
            $stmt = execute_query($this->db, $update_sql, "sisi", [
                $add_result['transaction_id'],
                $admin_user_id,
                $admin_notes,
                $commission_id
            ]);

            if (!$stmt) {
                throw new Exception('Failed to update commission status');
            }
            mysqli_stmt_close($stmt);

            // Update affiliate code stats
            $update_affiliate_sql = "UPDATE affiliate_codes SET
                                   total_referrals = total_referrals + 1,
                                   total_commissions = total_commissions + ?
                                   WHERE user_id = ?";
            $stmt = execute_query($this->db, $update_affiliate_sql, "di", [
                $commission['commission_amount'],
                $commission['referrer_id']
            ]);
            if ($stmt) mysqli_stmt_close($stmt);

            // Update referral status to confirmed
            $update_referral_sql = "UPDATE affiliate_referrals SET status = 'confirmed' WHERE referrer_id = ? AND referred_id = ?";
            $stmt = execute_query($this->db, $update_referral_sql, "ii", [
                $commission['referrer_id'],
                $commission['referred_id']
            ]);
            if ($stmt) mysqli_stmt_close($stmt);

            mysqli_commit($this->db);

            return [
                'success' => true,
                'message' => 'Commission approved successfully',
                'commission_id' => $commission_id,
                'amount' => $commission['commission_amount']
            ];

        } catch (Exception $e) {
            mysqli_rollback($this->db);
            return ['success' => false, 'message' => $e->getMessage()];
        }
    }

    /**
     * Admin reject commission
     */
    public function rejectCommission($commission_id, $admin_user_id, $admin_notes = '') {
        $sql = "UPDATE affiliate_commissions SET
                status = 'rejected',
                approved_by = ?,
                admin_notes = ?,
                approved_at = NOW()
                WHERE id = ? AND status = 'pending_admin_review'";

        $stmt = execute_query($this->db, $sql, "isi", [$admin_user_id, $admin_notes, $commission_id]);

        if ($stmt) {
            $affected_rows = mysqli_stmt_affected_rows($stmt);
            mysqli_stmt_close($stmt);

            if ($affected_rows > 0) {
                return [
                    'success' => true,
                    'message' => 'Commission rejected successfully'
                ];
            } else {
                return [
                    'success' => false,
                    'message' => 'Commission not found or already processed'
                ];
            }
        }

        return ['success' => false, 'message' => 'Failed to reject commission'];
    }

    /**
     * Get pending commissions for admin review
     */
    public function getPendingCommissions() {
        $sql = "SELECT ac.*,
                       u1.username as referrer_username,
                       u2.username as referred_username,
                       COALESCE(o.id, pc.id) as order_number,
                       COALESCE(o.created_at, pc.created_at) as order_date
                FROM affiliate_commissions ac
                LEFT JOIN users u1 ON ac.referrer_id = u1.id
                LEFT JOIN users u2 ON ac.referred_id = u2.id
                LEFT JOIN orders o ON ac.order_id = o.id
                LEFT JOIN pc_orders pc ON ac.pc_order_id = pc.id
                WHERE ac.status = 'pending_admin_review'
                ORDER BY ac.created_at DESC";

        $result = mysqli_query($this->db, $sql);

        if ($result) {
            $commissions = [];
            while ($row = mysqli_fetch_assoc($result)) {
                $commissions[] = $row;
            }
            mysqli_free_result($result);
            return $commissions;
        }

        return [];
    }
}

// Initialize affiliate system
$affiliate_system = new AffiliateSystem($link, $credit_system);
?>
