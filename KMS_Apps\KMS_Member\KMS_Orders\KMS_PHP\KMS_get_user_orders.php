<?php
// Enable error reporting for debugging
ini_set('display_errors', 1);
error_reporting(E_ALL);

// Start session if not already started
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

header('Content-Type: application/json');
require_once 'config.php';

// Debug log
error_log("get_user_orders.php - Session ID: " . session_id());
error_log("get_user_orders.php - Session data: " . print_r($_SESSION, true));

// Check if user is logged in
if (!isset($_SESSION["loggedin"]) || $_SESSION["loggedin"] !== true) {
    error_log("get_user_orders.php - User not logged in");
    echo json_encode([
        'success' => false,
        'message' => 'Please login first.',
        'debug' => [
            'session_id' => session_id(),
            'logged_in' => isset($_SESSION["loggedin"]) ? $_SESSION["loggedin"] : 'not set',
            'user_id' => $_SESSION["id"] ?? 'not set'
        ]
    ]);
    exit;
}

try {
    $user_id = $_SESSION["id"];
    
    // Get user orders with detailed information
    $sql = "SELECT o.id, o.user_id, o.status, o.final_price, o.estimated_price, o.created_at, o.updated_at,
                   COALESCE(JSON_UNQUOTE(JSON_EXTRACT(o.services, '$[0]')), 'General Order') as service_type,
                   COALESCE(o.payment_method, 'Unknown') as payment_method,
                   1 as quantity,
                   COALESCE(o.notes, '') as notes,
                   CASE
                       WHEN o.status = 'pending' THEN 'Pending Payment'
                       WHEN o.status = 'processing' THEN 'Processing'
                       WHEN o.status = 'completed' THEN 'Completed'
                       WHEN o.status = 'cancelled' THEN 'Cancelled'
                       WHEN o.status = 'refunded' THEN 'Refunded'
                       ELSE o.status
                   END as status_display,
                   CONCAT('Order #', o.id, ' - ', COALESCE(JSON_UNQUOTE(JSON_EXTRACT(o.services, '$[0]')), 'General Order')) as order_summary,
                   COALESCE(o.final_price, o.estimated_price, 0) as order_total,
                   DATE_FORMAT(o.created_at, '%Y-%m-%d %H:%i:%s') as order_date,
                   DATE_FORMAT(o.updated_at, '%Y-%m-%d %H:%i:%s') as last_updated
            FROM orders o
            WHERE o.user_id = ?
            ORDER BY o.created_at DESC";
            
    $stmt = execute_query($link, $sql, "i", [$user_id]);
    
    if ($stmt) {
        $result = mysqli_stmt_get_result($stmt);
        $orders = [];
        
        while ($row = mysqli_fetch_assoc($result)) {
            // Format order details for better display
            $order_details = "Order ID: {$row['id']} - " . $row['service_type'];
            
            $orders[] = [
                'id' => $row['id'],
                'order_number' => 'KMS-' . str_pad($row['id'], 6, '0', STR_PAD_LEFT),
                'order_details' => $order_details,
                'order_summary' => $row['order_summary'],
                'status' => $row['status'],
                'status_display' => $row['status_display'],
                'price' => number_format($row['final_price'] ?? $row['estimated_price'] ?? 0, 2),
                'final_price' => number_format($row['order_total'], 2),
                'payment_method' => $row['payment_method'],
                'created_at' => $row['order_date'],
                'updated_at' => $row['last_updated'],
                'service_type' => $row['service_type'],
                'quantity' => $row['quantity'],
                'notes' => $row['notes']
            ];
        }
        
        mysqli_stmt_close($stmt);
        
        // Also get PC orders
        $pc_sql = "SELECT po.*,
                          CASE
                              WHEN po.status = 'pending' THEN 'Pending Payment'
                              WHEN po.status = 'processing' THEN 'Processing'
                              WHEN po.status = 'completed' THEN 'Completed'
                              WHEN po.status = 'cancelled' THEN 'Cancelled'
                              WHEN po.status = 'shipped' THEN 'Shipped'
                              ELSE po.status
                          END as status_display,
                          DATE_FORMAT(po.created_at, '%Y-%m-%d %H:%i:%s') as order_date,
                          DATE_FORMAT(po.updated_at, '%Y-%m-%d %H:%i:%s') as last_updated
                   FROM pc_orders po
                   WHERE po.user_id = ?
                   ORDER BY po.created_at DESC";
                   
        $pc_stmt = execute_query($link, $pc_sql, "i", [$user_id]);
        
        if ($pc_stmt) {
            $pc_result = mysqli_stmt_get_result($pc_stmt);
            
            while ($row = mysqli_fetch_assoc($pc_result)) {
                $pc_details = "PC Order - " . ($row['pc_name'] ?? 'Custom PC Build');
                
                $orders[] = [
                    'id' => 'PC-' . $row['id'],
                    'order_number' => 'KMS-PC-' . str_pad($row['id'], 6, '0', STR_PAD_LEFT),
                    'order_details' => $pc_details,
                    'order_summary' => $pc_details,
                    'status' => $row['status'],
                    'status_display' => $row['status_display'],
                    'price' => number_format($row['total_price'] ?? 0, 2),
                    'final_price' => number_format($row['total_price'] ?? 0, 2),
                    'payment_method' => $row['payment_method'] ?? 'Unknown',
                    'created_at' => $row['order_date'],
                    'updated_at' => $row['last_updated'],
                    'service_type' => 'PC Build',
                    'quantity' => 1,
                    'notes' => $row['notes'] ?? '',
                    'type' => 'pc_order'
                ];
            }
            
            mysqli_stmt_close($pc_stmt);
        }
        
        // Sort all orders by created_at
        usort($orders, function($a, $b) {
            return strtotime($b['created_at']) - strtotime($a['created_at']);
        });
        
        echo json_encode([
            'success' => true,
            'orders' => $orders,
            'total_orders' => count($orders)
        ]);
    } else {
        echo json_encode([
            'success' => false,
            'message' => 'Failed to fetch orders.'
        ]);
    }
    
    close_db_connection($link);
} catch (Exception $e) {
    error_log("Error in get_user_orders.php: " . $e->getMessage());
    echo json_encode([
        'success' => false,
        'message' => 'An error occurred while fetching orders.',
        'debug' => [
            'error' => $e->getMessage(),
            'line' => $e->getLine(),
            'file' => $e->getFile(),
            'session_id' => session_id(),
            'user_id' => $_SESSION["id"] ?? 'not set'
        ]
    ]);
}
?>
