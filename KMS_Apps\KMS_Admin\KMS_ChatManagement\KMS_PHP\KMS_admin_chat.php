<?php
require_once __DIR__ . '/config.php';

// --- Admin Section - Security Check ---
if (!isset($_SESSION['is_admin']) || $_SESSION['is_admin'] !== true) {
    header("Location: ../index.php"); // Redirect non-admins
    exit;
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin - Live Chat</title>
    <style>
        body { font-family: Arial, sans-serif; background-color: #f4f4f4; margin: 0; }
        .admin-chat-container { display: flex; height: 100vh; }
        .session-list { width: 300px; border-right: 1px solid #ccc; background: #fff; overflow-y: auto; }
        .chat-panel { flex-grow: 1; display: flex; flex-direction: column; }
        .chat-history { flex-grow: 1; padding: 20px; overflow-y: auto; background: #e5ddd5; }
        .chat-input { padding: 10px; background: #f0f0f0; border-top: 1px solid #ccc; }
        .chat-input form { display: flex; }
        .chat-input input { flex-grow: 1; padding: 10px; border: 1px solid #ddd; border-radius: 20px; }
        .session-item { padding: 15px; border-bottom: 1px solid #eee; cursor: pointer; }
        .session-item:hover { background: #f0f0f0; }
        .session-item.active { background: #007bff; color: white; }
        .session-item .unread-count { float: right; background: red; color: white; border-radius: 50%; width: 20px; height: 20px; text-align: center; line-height: 20px; font-size: 12px; }
        .chat-actions { padding: 10px; background: #f0f0f0; border-top: 1px solid #ccc; text-align: right;}
        .chat-actions button { padding: 8px 15px; border: none; border-radius: 5px; cursor: pointer; margin-left: 10px;}
        .chat-actions .accept-btn { background: #28a745; color: white; }
        .chat-actions .close-btn { background: #dc3545; color: white; }
        h2 { text-align: center; margin: 15px 0; }
        .no-chat-selected { text-align: center; margin-top: 50px; color: #777;}
    </style>
</head>
<body>
    <div class="admin-chat-container">
        <div class="session-list">
            <h2>Chat Sessions</h2>
            <div id="sessionListContainer">
                <!-- Sessions will be loaded here -->
            </div>
        </div>
        <div class="chat-panel">
            <div id="chat-panel-content">
                <div class="no-chat-selected">
                    <p>Select a chat from the left to begin.</p>
                </div>
            </div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', () => {
            const sessionListContainer = document.getElementById('sessionListContainer');
            const chatPanelContent = document.getElementById('chat-panel-content');

            let activeSessionGuid = null;
            let messagePollingInterval = null;
            let lastMessageId = 0;

            // --- CORE FUNCTIONS ---

            // Fetch and render the list of chat sessions
            async function fetchSessions() {
                try {
                    const response = await fetch('chat_admin_get_sessions.php');
                    const data = await response.json();
                    if (data.success) {
                        renderSessionList(data.sessions);
                    } else {
                        console.error('Failed to fetch sessions:', data.message);
                    }
                } catch (error) {
                    console.error('Error fetching sessions:', error);
                }
            }

            // Render the list of sessions on the left
            function renderSessionList(sessions) {
                sessionListContainer.innerHTML = '';
                sessions.forEach(session => {
                    const sessionDiv = document.createElement('div');
                    sessionDiv.className = 'session-item';
                    if (session.session_guid === activeSessionGuid) {
                        sessionDiv.classList.add('active');
                    }
                    sessionDiv.dataset.guid = session.session_guid;
                    sessionDiv.dataset.status = session.status;
                    
                    const unreadCount = session.unread_messages > 0 ? `<span class="unread-count">${session.unread_messages}</span>` : '';
                    sessionDiv.innerHTML = `
                        <strong>${session.guest_nickname || 'Member Chat'}</strong> (${session.status}) ${unreadCount}
                        <br><small>${new Date(session.created_at).toLocaleString()}</small>
                    `;
                    sessionDiv.addEventListener('click', () => loadSession(session.session_guid, session.status));
                    sessionListContainer.appendChild(sessionDiv);
                });
            }

            // Load a specific chat session into the main panel
            function loadSession(guid, status) {
                if(messagePollingInterval) clearInterval(messagePollingInterval);
                
                activeSessionGuid = guid;
                lastMessageId = 0;
                
                // Highlight the active session
                document.querySelectorAll('.session-item').forEach(item => {
                    item.classList.toggle('active', item.dataset.guid === guid);
                });

                // Build the chat panel UI
                chatPanelContent.innerHTML = `
                    <div class="chat-history" id="chatHistory"></div>
                    <div class="chat-actions">
                        <button class="accept-btn" id="acceptBtn" style="display: ${status === 'pending' ? 'inline-block' : 'none'}">Accept</button>
                        <button class="close-btn" id="closeBtn" style="display: ${status === 'active' ? 'inline-block' : 'none'}">Close Chat</button>
                    </div>
                    <div class="chat-input">
                        <form id="adminMessageForm">
                            <input type="text" id="adminMessageInput" placeholder="Type your message..." autocomplete="off">
                            <button type="submit">Send</button>
                        </form>
                    </div>
                `;

                // Add event listeners for the new elements
                document.getElementById('adminMessageForm').addEventListener('submit', handleSendMessage);
                document.getElementById('acceptBtn')?.addEventListener('click', handleAcceptChat);
                document.getElementById('closeBtn')?.addEventListener('click', handleCloseChat);

                // Start fetching messages for this session
                fetchMessages();
                messagePollingInterval = setInterval(fetchMessages, 3000);
            }
            
            // Fetch messages for the active session
            async function fetchMessages() {
                if (!activeSessionGuid) return;
                try {
                    const response = await fetch(`chat_get_messages.php?session_guid=${activeSessionGuid}&last_message_id=${lastMessageId}`);
                    const data = await response.json();
                    if (data.success && data.messages.length > 0) {
                        data.messages.forEach(msg => {
                            addMessageToHistory(msg.sender, msg.message);
                            lastMessageId = msg.id;
                        });
                    }
                } catch(error) {
                    console.error('Error fetching messages:', error);
                }
            }

            function addMessageToHistory(sender, message) {
                const historyContainer = document.getElementById('chatHistory');
                if(!historyContainer) return;
                const msgDiv = document.createElement('div');
                msgDiv.className = `chat-message ${sender}`; // Ensure you have CSS for .chat-message.admin and .chat-message.user
                msgDiv.textContent = message;
                historyContainer.appendChild(msgDiv);
                historyContainer.scrollTop = historyContainer.scrollHeight;
            }

            // --- EVENT HANDLERS ---
            
            async function handleSendMessage(e) {
                e.preventDefault();
                const input = document.getElementById('adminMessageInput');
                const message = input.value.trim();
                if (message && activeSessionGuid) {
                    input.value = '';
                    addMessageToHistory('admin', message); // Optimistic update
                    await fetch('chat_send_message.php', {
                        method: 'POST',
                        headers: {'Content-Type': 'application/json'},
                        body: JSON.stringify({
                            session_guid: activeSessionGuid,
                            sender: 'admin',
                            message: message
                        })
                    });
                }
            }

            async function handleAcceptChat() {
                await fetch('chat_admin_accept.php', {
                    method: 'POST',
                    headers: {'Content-Type': 'application/json'},
                    body: JSON.stringify({ session_guid: activeSessionGuid })
                });
                fetchSessions(); // Refresh list to show status change
                // Also update the UI immediately
                document.getElementById('acceptBtn').style.display = 'none';
                document.getElementById('closeBtn').style.display = 'inline-block';
            }

            async function handleCloseChat() {
                 await fetch('chat_admin_close.php', {
                    method: 'POST',
                    headers: {'Content-Type': 'application/json'},
                    body: JSON.stringify({ session_guid: activeSessionGuid })
                });
                if(messagePollingInterval) clearInterval(messagePollingInterval);
                activeSessionGuid = null;
                chatPanelContent.innerHTML = '<div class="no-chat-selected"><p>Chat closed. Select another chat.</p></div>';
                fetchSessions(); // Refresh list to remove the closed chat
            }

            // --- INITIALIZATION ---
            fetchSessions();
            setInterval(fetchSessions, 10000); // Periodically refresh the session list
        });
    </script>
</body>
</html>