<?php
/**
 * PayPal Payment Redirect Page
 * This is a demo implementation for PayPal integration
 */

require_once 'config.php';
require_once 'credit_system.php';

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    header('Location: ../index.php');
    exit;
}

$transaction_id = $_GET['transaction_id'] ?? '';

if (empty($transaction_id)) {
    die('Invalid transaction ID');
}

// Get deposit details
$sql = "SELECT dr.*, ct.user_id FROM deposit_records dr 
        JOIN credit_transactions ct ON dr.transaction_id = ct.transaction_id 
        WHERE dr.transaction_id = ? AND dr.user_id = ?";
$stmt = execute_query($link, $sql, "si", [$transaction_id, $_SESSION['user_id']]);

if (!$stmt) {
    die('Database error');
}

$result = mysqli_stmt_get_result($stmt);
$deposit = mysqli_fetch_assoc($result);
mysqli_stmt_close($stmt);

if (!$deposit) {
    die('Deposit not found');
}

if ($deposit['payment_status'] !== 'pending') {
    die('Deposit already processed');
}

// In a real implementation, you would:
// 1. Create PayPal payment request
// 2. Redirect to PayPal
// 3. Handle PayPal callback
// 4. Update deposit status

// For demo purposes, we'll simulate the payment process
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PayPal Payment - KMS Credit</title>
    <style>
        body { font-family: Arial, sans-serif; background-color: #f5f5f5; margin: 0; padding: 20px; }
        .container { max-width: 600px; margin: auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        h1 { color: #0070ba; text-align: center; }
        .payment-details { background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0; }
        .payment-details h3 { margin-top: 0; color: #333; }
        .detail-row { display: flex; justify-content: space-between; margin: 10px 0; }
        .detail-row strong { color: #333; }
        .buttons { text-align: center; margin-top: 30px; }
        .btn { padding: 12px 30px; margin: 0 10px; border: none; border-radius: 5px; cursor: pointer; font-size: 16px; text-decoration: none; display: inline-block; }
        .btn-primary { background-color: #0070ba; color: white; }
        .btn-primary:hover { background-color: #005ea6; }
        .btn-secondary { background-color: #6c757d; color: white; }
        .btn-secondary:hover { background-color: #5a6268; }
        .demo-notice { background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 5px; margin: 20px 0; }
        .demo-notice h4 { margin-top: 0; color: #856404; }
    </style>
</head>
<body>
    <div class="container">
        <h1>PayPal Payment</h1>
        
        <div class="demo-notice">
            <h4>🚧 Demo Mode</h4>
            <p>This is a demonstration of the PayPal integration. In a real implementation, you would be redirected to PayPal's secure payment page.</p>
        </div>
        
        <div class="payment-details">
            <h3>Payment Details</h3>
            <div class="detail-row">
                <span>Transaction ID:</span>
                <strong><?= htmlspecialchars($transaction_id) ?></strong>
            </div>
            <div class="detail-row">
                <span>Amount:</span>
                <strong>$<?= number_format($deposit['amount'], 2) ?></strong>
            </div>
            <div class="detail-row">
                <span>Payment Method:</span>
                <strong>PayPal</strong>
            </div>
            <div class="detail-row">
                <span>Merchant:</span>
                <strong>KelvinKMS.com</strong>
            </div>
        </div>
        
        <div class="buttons">
            <button class="btn btn-primary" onclick="simulatePayment()">Simulate Payment Success</button>
            <button class="btn btn-secondary" onclick="simulateCancel()">Cancel Payment</button>
            <a href="member.php" class="btn btn-secondary">Back to Dashboard</a>
        </div>
    </div>

    <script>
        function simulatePayment() {
            if (confirm('Simulate successful PayPal payment?')) {
                // In real implementation, this would be handled by PayPal callback
                const formData = new FormData();
                formData.append('action', 'simulate_payment_success');
                formData.append('transaction_id', '<?= $transaction_id ?>');
                formData.append('external_payment_id', 'PAYPAL_' + Date.now());
                
                fetch('paypal_callback.php', {
                    method: 'POST',
                    body: formData
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert('Payment successful! Your credit has been added to your account.');
                        window.location.href = 'member.php';
                    } else {
                        alert('Payment processing failed: ' + data.message);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('Network error occurred');
                });
            }
        }
        
        function simulateCancel() {
            if (confirm('Cancel this payment?')) {
                const formData = new FormData();
                formData.append('action', 'cancel_deposit');
                formData.append('transaction_id', '<?= $transaction_id ?>');
                
                fetch('credit_deposit.php', {
                    method: 'POST',
                    body: formData
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert('Payment cancelled.');
                        window.location.href = 'member.php';
                    } else {
                        alert('Error cancelling payment: ' + data.message);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('Network error occurred');
                });
            }
        }
    </script>
</body>
</html>
