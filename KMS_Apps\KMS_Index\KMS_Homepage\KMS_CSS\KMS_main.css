/* Main Styles for KelvinKMS.com */
body {
    font-family: Arial, sans-serif;
    margin: 0;
    padding: 0;
    display: flex;
    justify-content: center;
    font-size: 20px;
    background-color: black;
}

.container {
    width: 100%;
    margin: 0 auto;
    text-align: center;
}

.section {
    padding: 4px;
    margin: 2px auto;
    border-radius: 16px;
    border: 3px solid;
    color: white;
    text-align: center;
    width: 98%;
    font-size: 20px;
    text-shadow: 2px 2px 4px black;
}

/* Section Colors */
#home { 
    background-color: rgb(17, 196, 206); 
    border-color: rgb(0 248 255); 
    height: auto; 
}

#portfolio { 
    background-color: #73c873; 
    border-color: #00ff00; 
}

#optimize { 
    background-color: rgb(193, 187, 12); 
    border-color: rgb(236, 232, 12); 
}

#print-service { 
    background-color: rgb(215, 185, 11); 
    border-color: rgb(255, 191, 73); 
}

#website-service { 
    background-color: #0567a6; 
    border-color: #008cff; 
}

#question-consult { 
    background-color: #ff7500; 
    border-color: #ffbf00; 
}

#useful-softwares { 
    background-color: #20B2AA; 
    border-color: #00ffff; 
}

#best-music { 
    background-color: rgb(193, 14, 202); 
    border-color: rgb(255, 0, 234); 
}

#copyright { 
    background-color: #164b7b; 
    border-color: #0084ff; 
}

/* Navigation */
.nav { 
    position: fixed; 
    right: 10px; 
    top: 50%; 
    transform: translateY(-50%); 
    display: flex; 
    flex-direction: column; 
}

.nav button { 
    margin: 2px 0; 
    padding: 6px; 
    cursor: pointer; 
    border-radius: 16px; 
    border: 3px solid; 
    font-size: 20px; 
}

.nav button:hover { 
    background-color: rgb(0, 255, 255); 
}

.nav button, .mobile-nav button { 
    text-shadow: 1px 1px 3px black; 
}

.mobile-nav { 
    display: none; 
    position: fixed; 
    right: 10px; 
    bottom: 10px; 
}

.mobile-nav button { 
    padding: 15px; 
    border-radius: 50%; 
    background-color: #444; 
    color: white; 
    border: none; 
    font-size: 18px; 
    cursor: pointer; 
}

/* Slideshow */
.slideshow-container { 
    position: relative; 
    max-width: 700px; 
    margin: auto; 
    height: 700px; 
    overflow: hidden; 
}

.slide { 
    display: none; 
    width: 100%; 
    height: 100%; 
    object-fit: cover; 
}

/* Mobile Responsive */
@media screen and (max-width: 768px) {
    .nav { 
        display: none; 
    }
    .mobile-nav { 
        display: block; 
    }
}

/* Site Info and Authentication */
.site-info {
    position: fixed;
    top: 15px;
    left: 15px;
    background-color: rgb(25 33 207 / 60%);
    padding: 10px 15px;
    border-radius: 20px;
    color: white;
    text-align: left;
    z-index: 1000;
}

.view-counter { 
    font-size: 20px; 
    margin-bottom: 10px; 
}

.auth-buttons button {
    display: block;
    width: 100%;
    padding: 8px;
    margin-bottom: 5px;
    border-radius: 12px;
    background-color: #0f987cb3;
    color: white;
    cursor: pointer;
    font-size: 20px;
}

.auth-buttons button:hover { 
    background-color: #0f987cb3; 
}

/* Logout button positioning */
.logout-btn {
    position: fixed;
    top: 15px;
    right: 15px;
    background-color: #e74c3c;
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 25px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    z-index: 1000;
    transition: background-color 0.3s ease;
}

.logout-btn:hover {
    background-color: #c0392b;
}

/* Modal Styles */
.modal {
    display: none;
    position: fixed;
    z-index: 1001;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    overflow: hidden;
    background-color: #1599c7;
}

.modal-content {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background-color: #1a86c1;
    color: white;
    padding: 20px;
    border: 5px solid #00c9ff;
    width: 90%;
    max-width: 400px;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.5);
    text-align: center;
    max-height: 90vh;
    overflow-y: auto;
}

.close-btn {
    color: #aaa;
    float: right;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
}

.close-btn:hover, .close-btn:focus {
    color: #fff;
}

.modal-content input, .modal-content select {
    width: calc(100% - 20px);
    padding: 10px;
    margin: 8px 0;
    border-radius: 12px;
    border: 3px solid #00bdff;
    background-color: #1558b7;
    color: white;
    font-size: 28px;
}

.modal-content select option {
    background-color: #00ff77;
    color: white;
}

.form-row {
    display: flex;
    gap: 20px;
}

.form-row input {
    flex: 1;
}

.modal-content button {
    display: inline-block;
    width: auto;
    padding: 12px;
    border: 3px solid #2eff00;
    border-radius: 16px;
    background-color: #57d30f;
    color: white;
    font-size: 28px;
    cursor: pointer;
    margin-top: 10px;
    box-shadow: 0 2px 8px rgb(0 0 0);
}

.modal-content button:hover {
    background-color: #00fff2;
    box-shadow: 0 2px 8px rgb(0 0 0);
}
