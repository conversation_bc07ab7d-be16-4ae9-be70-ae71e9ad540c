<?php
header('Content-Type: application/json');
require_once __DIR__ . '/config.php';

// --- Admin Section - Security Check ---
if (!isset($_SESSION['is_admin']) || $_SESSION['is_admin'] !== true) {
    http_response_code(403);
    echo json_encode(['success' => false, 'message' => 'Access denied.']);
    exit;
}

$response = ['success' => false, 'message' => 'An unknown error occurred.'];
$link = get_db_connection();

try {
    $data = json_decode(file_get_contents('php://input'), true);
    $session_guid = $data['session_guid'] ?? null;

    if (empty($session_guid)) {
        throw new Exception('Session GUID is required.');
    }

    // Update the status to 'closed' and set the ended_at timestamp
    $sql = "UPDATE chat_sessions SET status = 'closed', ended_at = CURRENT_TIMESTAMP WHERE session_guid = ?";
    $stmt = mysqli_prepare($link, $sql);
    
    if (!$stmt) {
        throw new Exception('Failed to prepare statement: ' . mysqli_error($link));
    }

    mysqli_stmt_bind_param($stmt, "s", $session_guid);
    
    if (mysqli_stmt_execute($stmt)) {
        if (mysqli_stmt_affected_rows($stmt) > 0) {
            $response['success'] = true;
            $response['message'] = 'Chat session has been closed.';
        } else {
            $response['message'] = 'Session does not exist or was already closed.';
        }
    } else {
        throw new Exception('Failed to close chat session.');
    }

    mysqli_stmt_close($stmt);

} catch (Exception $e) {
    $response['message'] = $e->getMessage();
    error_log("Chat Admin Close Error: " . $e->getMessage());
} finally {
    if ($link) {
        close_db_connection($link);
    }
}

echo json_encode($response);
?>