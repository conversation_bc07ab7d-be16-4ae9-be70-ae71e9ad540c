<?php
/**
 * KMS Affiliate API
 * Handles affiliate-related operations for users
 */

// Enable error reporting and log to file
ini_set('display_errors', 0);
error_reporting(E_ALL);

// Set error log file
$error_log = __DIR__ . '/../logs/api_errors.log';
if (!is_dir(dirname($error_log))) {
    mkdir(dirname($error_log), 0755, true);
}
ini_set('error_log', $error_log);

// Start output buffering to prevent any accidental output
ob_start();

// Log the start of the request
error_log("\n" . str_repeat('=', 80));
error_log('[' . date('Y-m-d H:i:s') . '] Starting API request: ' . ($_SERVER['REQUEST_URI'] ?? 'unknown'));
error_log('Request method: ' . ($_SERVER['REQUEST_METHOD'] ?? 'unknown'));
error_log('Session ID: ' . (session_id() ?: 'none'));

// Helper function to check if headers have been sent
function die_if_headers_sent() {
    if (headers_sent($file, $line)) {
        $error = sprintf('Headers already sent in %s on line %d', $file, $line);
        error_log($error);
        die('{"success":false,"message":"Server error: Headers already sent"}');
    }
}

// Helper function to send JSON response
function send_json_response($data, $status_code = 200) {
    // Clear any previous output
    while (ob_get_level() > 0) ob_end_clean();
    
    // Set headers
    http_response_code($status_code);
    header('Content-Type: application/json');
    
    // Output JSON
    echo json_encode($data);
    exit;
}

try {
    // Start session
    if (session_status() === PHP_SESSION_NONE) {
        session_start();
    }

    // Include required files
    require_once 'config.php';
    require_once 'affiliate_system.php';

    // Set headers
    header('Access-Control-Allow-Origin: ' . ($_SERVER['HTTP_ORIGIN'] ?? '*'));
    header('Access-Control-Allow-Credentials: true');
    header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
    header('Access-Control-Allow-Headers: X-Requested-With, Content-Type');

    // Handle preflight requests
    if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
        http_response_code(200);
        exit;
    }

    // Ensure we don't send any output before headers
    die_if_headers_sent();

    // Simple session check
    if (!isset($_SESSION['loggedin']) || $_SESSION['loggedin'] !== true) {
        send_json_response([
            'success' => false,
            'message' => 'Not logged in',
            'session' => [
                'id' => session_id(),
                'name' => session_name(),
                'loggedin' => $_SESSION['loggedin'] ?? 'not set',
                'user_id' => $_SESSION['id'] ?? 'not set'
            ]
        ], 401);
    }

    // Get user ID and action
    $user_id = (int)$_SESSION['id'];
    $action = $_POST['action'] ?? $_GET['action'] ?? '';

    // Initialize affiliate system
    $affiliate_system = new AffiliateSystem($link, $credit_system);

    try {
        // Handle different actions
        switch ($action) {
            case 'get_affiliate_info':
                getAffiliateInfo($affiliate_system, $user_id);
                break;
                
            case 'get_affiliate_stats':
                getAffiliateStats($affiliate_system, $user_id);
                break;
                
            case 'get_referral_history':
                getReferralHistory($affiliate_system, $user_id);
                break;
                
            case 'request_withdrawal':
                requestWithdrawal($affiliate_system, $user_id);
                break;
                
            case 'get_withdrawal_history':
                getWithdrawalHistory($user_id);
                break;
                
            case 'cancel_withdrawal':
                cancelWithdrawal($user_id);
                break;
                
            default:
                send_json_response([
                    'success' => false,
                    'message' => 'Invalid action'
                ], 400);
        }
    } catch (Exception $e) {
        $errorDetails = [
            'message' => $e->getMessage(),
            'file' => $e->getFile(),
            'line' => $e->getLine(),
            'trace' => $e->getTraceAsString(),
            'session' => $_SESSION ?? []
        ];
        
        error_log('API Error: ' . print_r($errorDetails, true));
        
        // For security, only send the error message in production
        $errorMessage = 'An error occurred';
        if (ini_get('display_errors')) {
            $errorMessage .= ': ' . $e->getMessage();
        }
        
        send_json_response([
            'success' => false,
            'message' => $errorMessage
        ], 500);
    }
} catch (Exception $e) {
    // This is a fallback error handler in case something goes wrong in the main try block
    $errorDetails = [
        'message' => $e->getMessage(),
        'file' => $e->getFile(),
        'line' => $e->getLine(),
        'trace' => $e->getTraceAsString(),
        'session' => $_SESSION ?? []
    ];
    
    error_log('FATAL API Error: ' . print_r($errorDetails, true));
    
    // For security, only send the error message in development
    $errorMessage = 'A fatal error occurred';
    if (ini_get('display_errors')) {
        $errorMessage .= ': ' . $e->getMessage();
    }
    
    // Make sure we can still send a response
    if (!headers_sent()) {
        header('Content-Type: application/json', true, 500);
    }
    
    die(json_encode([
        'success' => false,
        'message' => $errorMessage
    ]));
}

/**
 * Get user's affiliate information
 */
function getAffiliateInfo($affiliate_system, $user_id) {
    $affiliate = $affiliate_system->getUserAffiliateCode($user_id);
    
    if ($affiliate) {
        send_json_response([
            'success' => true,
            'affiliate' => [
                'code' => $affiliate['affiliate_code'],
                'is_active' => (bool)$affiliate['is_active'],
                'total_referrals' => $affiliate['total_referrals'],
                'total_commissions' => number_format($affiliate['total_commissions'], 2),
                'total_withdrawn' => number_format($affiliate['total_withdrawn'], 2),
                'referral_url' => 'https://' . $_SERVER['HTTP_HOST'] . '/index.php?ref=' . $affiliate['affiliate_code'],
                'created_at' => $affiliate['created_at']
            ]
        ]);
    } else {
        echo json_encode(['success' => false, 'message' => 'Failed to get affiliate information']);
    }
}

/**
 * Get affiliate statistics
 */
function getAffiliateStats($affiliate_system, $user_id) {
    $stats = $affiliate_system->getAffiliateStats($user_id);
    
    if ($stats) {
        echo json_encode([
            'success' => true,
            'stats' => [
                'total_referrals' => $stats['total_referrals'],
                'confirmed_referrals' => $stats['confirmed_referrals'],
                'pending_referrals' => $stats['pending_referrals'],
                'total_commissions' => number_format($stats['total_commissions'], 2),
                'paid_commissions' => number_format($stats['paid_commissions'], 2),
                'pending_commissions' => number_format($stats['pending_commissions'], 2),
                'commission_balance' => number_format($stats['commission_balance'], 2),
                'total_withdrawn' => number_format($stats['total_withdrawn'], 2)
            ]
        ]);
    } else {
        echo json_encode(['success' => false, 'message' => 'Failed to get affiliate statistics']);
    }
}

/**
 * Get referral history
 */
function getReferralHistory($affiliate_system, $user_id) {
    $page = intval($_GET['page'] ?? 1);
    $limit = intval($_GET['limit'] ?? 20);
    $offset = ($page - 1) * $limit;
    
    $referrals = $affiliate_system->getReferralHistory($user_id, $limit, $offset);
    
    $formatted_referrals = [];
    foreach ($referrals as $referral) {
        $formatted_referrals[] = [
            'id' => $referral['id'],
            'referred_username' => $referral['referred_username'],
            'referred_email' => $referral['referred_email'],
            'status' => $referral['status'],
            'commission_amount' => $referral['commission_amount'] ? number_format($referral['commission_amount'], 2) : '0.00',
            'commission_status' => $referral['commission_status'] ?? 'pending',
            'created_at' => date('Y-m-d H:i:s', strtotime($referral['created_at'])),
            'confirmed_at' => $referral['confirmed_at'] ? date('Y-m-d H:i:s', strtotime($referral['confirmed_at'])) : null,
            'paid_at' => $referral['paid_at'] ? date('Y-m-d H:i:s', strtotime($referral['paid_at'])) : null
        ];
    }
    
    echo json_encode([
        'success' => true,
        'referrals' => $formatted_referrals,
        'pagination' => [
            'page' => $page,
            'limit' => $limit,
            'has_more' => count($referrals) == $limit
        ]
    ]);
}

/**
 * Request commission withdrawal
 */
function requestWithdrawal($affiliate_system, $user_id) {
    global $link;
    
    $amount = floatval($_POST['amount'] ?? 0);
    $method = trim($_POST['method'] ?? '');
    $details = $_POST['details'] ?? [];
    
    if ($amount <= 0) {
        echo json_encode(['success' => false, 'message' => 'Invalid withdrawal amount']);
        return;
    }
    
    if (empty($method)) {
        echo json_encode(['success' => false, 'message' => 'Withdrawal method is required']);
        return;
    }
    
    // Get minimum withdrawal amount
    $min_amount = getWithdrawalSettings()['min_withdrawal_amount'];
    if ($amount < $min_amount) {
        echo json_encode([
            'success' => false, 
            'message' => "Minimum withdrawal amount is $" . number_format($min_amount, 2)
        ]);
        return;
    }
    
    // Check user's commission balance
    $sql = "SELECT commission_balance FROM user_wallets WHERE user_id = ?";
    $stmt = execute_query($link, $sql, "i", [$user_id]);
    
    if (!$stmt) {
        echo json_encode(['success' => false, 'message' => 'Database error']);
        return;
    }
    
    $result = mysqli_stmt_get_result($stmt);
    $wallet = mysqli_fetch_assoc($result);
    mysqli_stmt_close($stmt);
    
    if (!$wallet || $wallet['commission_balance'] < $amount) {
        echo json_encode(['success' => false, 'message' => 'Insufficient commission balance']);
        return;
    }
    
    mysqli_begin_transaction($link);
    
    try {
        // Generate unique request ID
        $request_id = 'WD' . date('Ymd') . strtoupper(substr(uniqid(), -6));
        
        // Create withdrawal request
        $sql = "INSERT INTO withdrawal_requests (user_id, request_id, amount, withdrawal_method, withdrawal_details, status) 
                VALUES (?, ?, ?, ?, ?, 'pending')";
        $details_json = json_encode($details);
        $stmt = execute_query($link, $sql, "isdss", [$user_id, $request_id, $amount, $method, $details_json]);
        
        if (!$stmt) {
            throw new Exception('Failed to create withdrawal request');
        }
        mysqli_stmt_close($stmt);
        
        // Deduct from commission balance (freeze it)
        $sql = "UPDATE user_wallets SET commission_balance = commission_balance - ? WHERE user_id = ?";
        $stmt = execute_query($link, $sql, "di", [$amount, $user_id]);
        
        if (!$stmt) {
            throw new Exception('Failed to update commission balance');
        }
        mysqli_stmt_close($stmt);
        
        mysqli_commit($link);
        
        echo json_encode([
            'success' => true,
            'message' => 'Withdrawal request submitted successfully',
            'request_id' => $request_id
        ]);
        
    } catch (Exception $e) {
        mysqli_rollback($link);
        echo json_encode(['success' => false, 'message' => $e->getMessage()]);
    }
}

/**
 * Get withdrawal history
 */
function getWithdrawalHistory($user_id) {
    global $link;
    
    $page = intval($_GET['page'] ?? 1);
    $limit = intval($_GET['limit'] ?? 20);
    $offset = ($page - 1) * $limit;
    
    $sql = "SELECT * FROM withdrawal_requests 
            WHERE user_id = ? 
            ORDER BY requested_at DESC 
            LIMIT ? OFFSET ?";
    
    $stmt = execute_query($link, $sql, "iii", [$user_id, $limit, $offset]);
    
    if (!$stmt) {
        echo json_encode(['success' => false, 'message' => 'Database error']);
        return;
    }
    
    $result = mysqli_stmt_get_result($stmt);
    $withdrawals = [];
    
    while ($row = mysqli_fetch_assoc($result)) {
        $withdrawals[] = [
            'id' => $row['id'],
            'request_id' => $row['request_id'],
            'amount' => number_format($row['amount'], 2),
            'method' => $row['withdrawal_method'],
            'status' => $row['status'],
            'status_display' => getWithdrawalStatusDisplay($row['status']),
            'requested_at' => date('Y-m-d H:i:s', strtotime($row['requested_at'])),
            'processed_at' => $row['processed_at'] ? date('Y-m-d H:i:s', strtotime($row['processed_at'])) : null,
            'completed_at' => $row['completed_at'] ? date('Y-m-d H:i:s', strtotime($row['completed_at'])) : null,
            'admin_notes' => $row['admin_notes']
        ];
    }
    
    mysqli_stmt_close($stmt);
    
    echo json_encode([
        'success' => true,
        'withdrawals' => $withdrawals,
        'pagination' => [
            'page' => $page,
            'limit' => $limit,
            'has_more' => count($withdrawals) == $limit
        ]
    ]);
}

/**
 * Cancel withdrawal request
 */
function cancelWithdrawal($user_id) {
    global $link;
    
    $request_id = trim($_POST['request_id'] ?? '');
    
    if (empty($request_id)) {
        echo json_encode(['success' => false, 'message' => 'Request ID is required']);
        return;
    }
    
    mysqli_begin_transaction($link);
    
    try {
        // Get withdrawal request
        $sql = "SELECT * FROM withdrawal_requests WHERE request_id = ? AND user_id = ? AND status = 'pending'";
        $stmt = execute_query($link, $sql, "si", [$request_id, $user_id]);
        
        if (!$stmt) {
            throw new Exception('Database error');
        }
        
        $result = mysqli_stmt_get_result($stmt);
        $withdrawal = mysqli_fetch_assoc($result);
        mysqli_stmt_close($stmt);
        
        if (!$withdrawal) {
            throw new Exception('Withdrawal request not found or cannot be cancelled');
        }
        
        // Cancel the request
        $sql = "UPDATE withdrawal_requests SET status = 'cancelled' WHERE request_id = ? AND user_id = ?";
        $stmt = execute_query($link, $sql, "si", [$request_id, $user_id]);
        
        if (!$stmt) {
            throw new Exception('Failed to cancel withdrawal request');
        }
        mysqli_stmt_close($stmt);
        
        // Restore commission balance
        $sql = "UPDATE user_wallets SET commission_balance = commission_balance + ? WHERE user_id = ?";
        $stmt = execute_query($link, $sql, "di", [$withdrawal['amount'], $user_id]);
        
        if (!$stmt) {
            throw new Exception('Failed to restore commission balance');
        }
        mysqli_stmt_close($stmt);
        
        mysqli_commit($link);
        
        echo json_encode([
            'success' => true,
            'message' => 'Withdrawal request cancelled successfully'
        ]);
        
    } catch (Exception $e) {
        mysqli_rollback($link);
        echo json_encode(['success' => false, 'message' => $e->getMessage()]);
    }
}

/**
 * Get withdrawal settings
 */
function getWithdrawalSettings() {
    global $link;
    
    $defaults = [
        'min_withdrawal_amount' => 100.00,
        'withdrawal_fee_percentage' => 0.00,
        'withdrawal_fee_fixed' => 0.00
    ];
    
    $sql = "SELECT setting_key, setting_value FROM affiliate_settings 
            WHERE setting_key IN ('min_withdrawal_amount', 'withdrawal_fee_percentage', 'withdrawal_fee_fixed')";
    $result = mysqli_query($link, $sql);
    
    if ($result) {
        while ($row = mysqli_fetch_assoc($result)) {
            $defaults[$row['setting_key']] = (float)$row['setting_value'];
        }
    }
    
    return $defaults;
}

/**
 * Get display text for withdrawal status
 */
function getWithdrawalStatusDisplay($status) {
    $statuses = [
        'pending' => 'Pending Review',
        'processing' => 'Processing',
        'approved' => 'Approved',
        'completed' => 'Completed',
        'rejected' => 'Rejected',
        'cancelled' => 'Cancelled'
    ];
    
    return $statuses[$status] ?? ucfirst($status);
}
?>
