<?php
session_start();
require_once 'config.php';

header('Content-Type: application/json');

// User Check
if (!isset($_SESSION["loggedin"]) || $_SESSION["loggedin"] !== true) {
    echo json_encode(['success' => false, 'message' => 'Access denied. You must be logged in.']);
    exit;
}

$user_id = $_SESSION["id"];

// Fetch all orders for the logged-in user
$sql = "SELECT 
            id, 
            order_details, 
            status, 
            final_price, 
            estimated_completion_date, 
            created_at, 
            updated_at 
        FROM pc_orders 
        WHERE user_id = ?
        ORDER BY created_at DESC";

$stmt = execute_query($link, $sql, "i", [$user_id]);

if ($stmt) {
    $result = mysqli_stmt_get_result($stmt);
    $orders = mysqli_fetch_all($result, MYSQLI_ASSOC);
    mysqli_stmt_close($stmt);
    echo json_encode(['success' => true, 'orders' => $orders]);
} else {
    echo json_encode(['success' => false, 'message' => 'Failed to fetch your orders.']);
}

close_db_connection($link);

?>