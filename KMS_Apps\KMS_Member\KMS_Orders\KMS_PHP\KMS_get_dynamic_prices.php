<?php
/**
 * Dynamic Prices Helper Functions
 * Provides functions to get current pricing from database for display
 */

$base_path = dirname(dirname(dirname(dirname(dirname(__DIR__)))));
require_once $base_path . '/KMS_Apps/KMS_Core/KMS_Config/KMS_PHP/KMS_config.php';

/**
 * Get all active prices by category
 */
function getDynamicPrices($category = null) {
    // Use a fresh connection to ensure data consistency
    $local_link = get_db_connection();
    
    $sql = "SELECT * FROM service_prices WHERE is_active = 1";
    $params = [];
    $types = "";
    
    if ($category) {
        $sql .= " AND service_category = ?";
        $params[] = $category;
        $types .= "s";
    }
    
    $sql .= " ORDER BY service_category, sort_order, item_name";
    
    $prices = [];
    if (empty($params)) {
        $result = mysqli_query($local_link, $sql);
        if ($result) {
            $prices = mysqli_fetch_all($result, MYSQLI_ASSOC);
            mysqli_free_result($result);
        }
    } else {
        $stmt = execute_query($local_link, $sql, $types, $params);
        if ($stmt) {
            $result = mysqli_stmt_get_result($stmt);
            $prices = mysqli_fetch_all($result, MYSQLI_ASSOC);
            mysqli_stmt_close($stmt);
        }
    }
    
    close_db_connection($local_link);
    return $prices;
}

/**
 * Get price by service type
 */
function getPriceByType($service_type) {
    // Use a fresh connection to ensure data consistency
    $local_link = get_db_connection();
    
    $sql = "SELECT * FROM service_prices WHERE service_type = ? AND is_active = 1 LIMIT 1";
    $stmt = execute_query($local_link, $sql, "s", [$service_type]);
    
    $price = null;
    if ($stmt) {
        $result = mysqli_stmt_get_result($stmt);
        $price = mysqli_fetch_assoc($result);
        mysqli_stmt_close($stmt);
    }
    
    close_db_connection($local_link);
    return $price;
}

/**
 * Format price for display with language support
 */
function formatPriceDisplay($price_data, $lang = 'en') {
    if (!$price_data) return '';
    
    $price = '$' . number_format($price_data['base_price'], 2);
    $unit = $price_data['unit'];
    
    // Get localized name
    $name = $price_data['item_name'];
    if ($lang === 'zh-CN' && !empty($price_data['item_name_zh'])) {
        $name = $price_data['item_name_zh'];
    } elseif ($lang === 'en' && !empty($price_data['item_name_en'])) {
        $name = $price_data['item_name_en'];
    }
    
    return $name . ' = ' . $price . ($unit !== 'each' ? ' ' . $unit : '');
}

/**
 * Get optimize service prices for homepage display
 */
function getOptimizePricesForHomepage($lang = 'en') {
    $prices = getDynamicPrices('optimize');
    $formatted_prices = [];
    
    foreach ($prices as $price) {
        $formatted_prices[$price['service_type']] = formatPriceDisplay($price, $lang);
    }
    
    return $formatted_prices;
}

/**
 * Get print service prices for homepage display
 * Returns array with preserved sort_order from database
 */
function getPrintPricesForHomepage($lang = 'en') {
    $prices = getDynamicPrices('print');
    $formatted_prices = [];
    
    foreach ($prices as $price) {
        // Preserve sort order by using indexed array with sort_order and service_type
        $formatted_prices[] = [
            'service_type' => $price['service_type'],
            'sort_order' => $price['sort_order'],
            'display' => formatPriceDisplay($price, $lang)
        ];
    }
    
    return $formatted_prices;
}

/**
 * Get discount information
 */
function getDiscountInfo($lang = 'en') {
    // This could be made dynamic in the future
    $discounts = [
        'photo_bulk' => $lang === 'zh-CN' ? '超過10張照片 = 九折優惠' : 'Over 10 photos = 10% discount',
        'print_bulk' => $lang === 'zh-CN' ? '打印超過10張可享九折優惠' : '10% discount applied when you print over 10 papers',
        'shipping' => $lang === 'zh-CN' ? '運費和處理費 = $3 ~ $10，根據體積和紙張尺寸而定' : 'Shipping & handling fee = $3 ~ $10 based on volume and paper size'
    ];
    
    return $discounts;
}

/**
 * Check if service prices table exists and has data
 */
function checkServicePricesTable() {
    // Use a fresh connection to ensure data consistency
    $local_link = get_db_connection();
    
    // Check if table exists
    $check_table = "SHOW TABLES LIKE 'service_prices'";
    $result = mysqli_query($local_link, $check_table);
    
    if (!$result || mysqli_num_rows($result) == 0) {
        close_db_connection($local_link);
        return false;
    }
    mysqli_free_result($result);
    
    // Check if table has data
    $check_data = "SELECT COUNT(*) as count FROM service_prices";
    $result = mysqli_query($local_link, $check_data);
    
    $has_data = false;
    if ($result) {
        $row = mysqli_fetch_assoc($result);
        $has_data = $row['count'] > 0;
        mysqli_free_result($result);
    }
    
    close_db_connection($local_link);
    return $has_data;
}

/**
 * Initialize service prices if table doesn't exist or is empty
 */
function initializeServicePrices() {
    if (!checkServicePricesTable()) {
        // Use a fresh connection to ensure data consistency
        $local_link = get_db_connection();
        
        // Run the setup script
        $setup_sql = file_get_contents(__DIR__ . '/../SQL/setup_service_prices.sql');
        if ($setup_sql) {
            // Split by semicolon and execute each statement
            $statements = explode(';', $setup_sql);
            foreach ($statements as $statement) {
                $statement = trim($statement);
                if (!empty($statement)) {
                    mysqli_query($local_link, $statement);
                }
            }
        }
        
        close_db_connection($local_link);
    }
}
?>
