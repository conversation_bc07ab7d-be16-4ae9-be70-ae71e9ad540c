<?php
session_start();

// Check if user is logged in and is admin
if (!isset($_SESSION["loggedin"]) || $_SESSION["loggedin"] !== true || !isset($_SESSION["is_admin"]) || $_SESSION["is_admin"] !== true) {
    header("location: ../index.php");
    exit;
}

require_once 'config.php';
require_once 'functions.php';
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>KMS Admin Dashboard</title>
    <link rel="stylesheet" href="../CSS/custom-modal.css" />
    <style>
        body { font-family: Arial, sans-serif; background-color: #a48f19; color: white; margin: 0; padding: 20px; }

        .container { max-width: 1200px; margin: auto; background-color: rgb(5 195 182); padding: 10px; border-radius: 10px; box-shadow: 0 2px 8px rgb(0 0 0); }

        h1 { color: #ffffff; text-align: center; font-size: 26px; }

        .admin-controls {
            text-align: center;
            margin-bottom: 20px;
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 10px;
            max-width: 1200px;
            margin: 0 auto 20px auto;
        }

        .admin-controls button:nth-child(9) {
            grid-column: 2 / 4;
        }

        .admin-controls button {
            padding: 10px 15px;
            margin: 0;
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-radius: 12px;
            background-color: rgb(253, 202, 0);
            font-size: 14px;
            color: rgb(255, 255, 255);
            cursor: pointer;
            font-weight: bold;
            box-shadow: 0 2px 8px rgb(0 0 0);
            transition: all 0.3s ease;
            text-align: center;
            min-height: 50px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .admin-controls button:hover {
            background-color: rgba(255, 255, 255, 0.5);
            border-color: rgba(255, 255, 255, 0.8);
        }

        #order-list { padding: 0; }

        .order-card {
            background-color: rgba(255, 255, 255, 0.1);
            border-left: 5px solid rgb(253, 202, 0);
            margin-bottom: 20px;
            padding: 20px;
            border-radius: 12px;
            box-shadow: 0 2px 8px rgb(0 0 0);
        }

        .order-card h3 { margin-top: 0; color: #ffffff; font-weight: bold; }
        .order-card p { margin: 5px 0; }
        .order-card ul { padding-left: 20px; }

        .order-actions { margin-top: 15px; }
        .order-actions select {
            padding: 8px;
            border-radius: 12px;
            background-color: rgb(255 194 0);
            color: #000;
            border: 2px solid rgb(253, 202, 0);
            box-shadow: 0 2px 8px rgb(0 0 0);
        }

        .status-pending { color: #ffa500; }
        .status-processing { color: #00bfff; }
        .status-completed { color: #32cd32; }
        .status-cancelled { color: #ff6347; }

        /* Logout button positioning */
        .logout-btn {
            position: fixed;
            top: 15px;
            right: 15px;
            background-color: #dc3545;
            color: white;
            border: 2px solid #dc3545;
            padding: 10px 20px;
            border-radius: 12px;
            cursor: pointer;
            font-size: 14px;
            font-weight: bold;
            z-index: 1000;
            box-shadow: 0 2px 8px rgb(0 0 0);
            transition: all 0.3s ease;
        }
        .logout-btn:hover {
            background-color: #c82333;
            border-color: #bd2130;
        }

        /* Price management styles */
        .price-section {
            margin-top: 10px;
            padding: 10px;
            background-color: #444;
            border-radius: 8px;
        }
        .price-input {
            width: 100px;
            padding: 5px;
            margin: 0 5px;
            border-radius: 3px;
            background-color: #333;
            color: white;
            border: 1px solid #666;
        }
        .price-btn {
            background-color: #2196F3;
            color: white;
            border: none;
            padding: 5px 10px;
            border-radius: 3px;
            cursor: pointer;
            font-size: 12px;
            margin-left: 5px;
        }
        .price-btn:hover { background-color: #1976D2; }
    </style>
</head>
<body>
    <!-- Logout button in top right -->
    <button class="logout-btn" onclick="confirmLogout()">Logout</button>

    <div class="container">
        <h1>Admin Dashboard - KelvinKMS.com</h1>
        <div class="admin-controls">
            <button onclick="location.reload()">🔄 Refresh Orders</button>
            <button onclick="location.href='admin_orders.php'">📋 Manage PC Orders</button>
            <button onclick="location.href='admin_pc_management.php'" style="background-color: #007bff;">💻 PC Components & Configs</button>
            <button onclick="location.href='admin_credit_dashboard.php'" style="background-color: #28a745;">💰 Credit Management</button>
            <button onclick="location.href='admin_member_management.php'" style="background-color: #17a2b8;">👥 Member Management</button>
            <button onclick="location.href='admin_optimize_prices.php'" style="background-color: #6f42c1;">📸 Optimize Prices</button>
            <button onclick="location.href='admin_print_prices.php'" style="background-color: #fd7e14;">🖨️ Print Prices</button>
            <button onclick="location.href='admin_service_orders.php'" style="background-color: #20c997;">🛒 Service Orders</button>
            <button onclick="location.href='admin_profile.php'" style="background-color: #dc3545;">👤 Admin Profile</button>
        </div>
        <div id="order-list">
            <?php
            // Get all orders from database with user details
            $sql = "SELECT o.*, u.email, u.first_name, u.last_name, u.phone_number FROM orders o
                    LEFT JOIN users u ON o.user_id = u.id
                    ORDER BY o.created_at DESC";
            $result = mysqli_query($link, $sql);

            if ($result && mysqli_num_rows($result) > 0) {
                while ($order = mysqli_fetch_assoc($result)) {
                    $services = json_decode($order['services'], true);
                    echo '<div class="order-card">';
                    echo '<h3>Order #' . $order['id'] . ' from: ' . htmlspecialchars($order['username']) . '</h3>';

                    // Customer details
                    if ($order['first_name'] && $order['last_name']) {
                        echo '<p><strong>Customer:</strong> ' . htmlspecialchars($order['first_name'] . ' ' . $order['last_name']) . '</p>';
                    }
                    if ($order['email']) {
                        echo '<p><strong>Email:</strong> ' . htmlspecialchars($order['email']) . '</p>';
                    }
                    if ($order['phone_number']) {
                        echo '<p><strong>Phone:</strong> ' . htmlspecialchars($order['phone_number']) . '</p>';
                    }

                    echo '<p><strong>Order Date:</strong> ' . date('Y-m-d H:i:s', strtotime($order['created_at'])) . '</p>';
                    echo '<p><strong>Status:</strong> <span class="status-' . $order['status'] . '">' . ucfirst($order['status']) . '</span></p>';

                    // Budget range if specified
                    if (!empty($order['budget_range'])) {
                        $budget_text = str_replace('_', ' ', $order['budget_range']);
                        $budget_text = str_replace('under', 'Under $', $budget_text);
                        $budget_text = str_replace('over', 'Over $', $budget_text);
                        echo '<p><strong>Budget Range:</strong> ' . htmlspecialchars($budget_text) . '</p>';
                    }

                    echo '<p><strong>Selected Services:</strong></p>';
                    echo '<ul>';
                    if ($services && is_array($services)) {
                        foreach ($services as $service) {
                            echo '<li>' . htmlspecialchars($service) . '</li>';
                        }
                    }
                    echo '</ul>';
                    echo '<p><strong>Notes:</strong> ' . (empty($order['notes']) ? 'None' : htmlspecialchars($order['notes'])) . '</p>';

                    // Price management section
                    echo '<div class="price-section">';
                    echo '<p><strong>Price Management:</strong></p>';
                    echo '<label>Estimated Price: $</label>';
                    echo '<input type="number" class="price-input" id="estimated_' . $order['id'] . '" value="' . ($order['estimated_price'] ?? '') . '" step="0.01" placeholder="0.00">';
                    echo '<button class="price-btn" onclick="updatePrice(' . $order['id'] . ', \'estimated\')">Update Estimate</button><br><br>';

                    echo '<label>Final Price: $</label>';
                    echo '<input type="number" class="price-input" id="final_' . $order['id'] . '" value="' . ($order['final_price'] ?? '') . '" step="0.01" placeholder="0.00">';
                    echo '<button class="price-btn" onclick="updatePrice(' . $order['id'] . ', \'final\')">Update Final</button>';
                    echo '</div>';

                    echo '<div class="order-actions">';
                    echo '<label>Status: </label>';
                    echo '<select onchange="updateOrderStatus(' . $order['id'] . ', this.value)">';
                    echo '<option value="pending"' . ($order['status'] == 'pending' ? ' selected' : '') . '>Pending</option>';
                    echo '<option value="processing"' . ($order['status'] == 'processing' ? ' selected' : '') . '>Processing</option>';
                    echo '<option value="completed"' . ($order['status'] == 'completed' ? ' selected' : '') . '>Completed</option>';
                    echo '<option value="cancelled"' . ($order['status'] == 'cancelled' ? ' selected' : '') . '>Cancelled</option>';
                    echo '</select>';
                    echo '</div>';
                    echo '</div>';
                }
            } else {
                echo '<p style="text-align:center;">No orders found.</p>';
            }
            ?>
        </div>
    </div>

    <script src="../JS/custom-modal.js"></script>
    <script>
        function updateOrderStatus(orderId, newStatus) {
            const statusTexts = {
                'pending': 'Pending',
                'processing': 'Processing',
                'completed': 'Completed',
                'cancelled': 'Cancelled'
            };

            showConfirm(
                'Update Order Status',
                `Are you sure you want to change this order status to "${statusTexts[newStatus]}"?`,
                () => {
                    showLoading('Updating Status', 'Please wait while we update the order status...');

                    const formData = new FormData();
                    formData.append('order_id', orderId);
                    formData.append('status', newStatus);

                    fetch('update_order_status.php', {
                        method: 'POST',
                        body: formData
                    })
                    .then(response => response.json())
                    .then(data => {
                        hideModal();
                        if (data.success) {
                            showSuccess('Status Updated', 'Order status has been updated successfully!', () => {
                                location.reload();
                            });
                        } else {
                            showError('Update Failed', data.message || 'Failed to update order status.');
                        }
                    })
                    .catch(error => {
                        hideModal();
                        console.error('Error:', error);
                        showError('Update Failed', 'Network error. Please try again.');
                    });
                }
            );
        }

        // Update order price
        function updatePrice(orderId, priceType) {
            const inputId = priceType + '_' + orderId;
            const price = document.getElementById(inputId).value;

            if (!price || price <= 0) {
                showError('Invalid Price', 'Please enter a valid price amount.');
                return;
            }

            showConfirm(
                'Update Price',
                `Are you sure you want to update the ${priceType} price to $${price}?`,
                () => {
                    showLoading('Updating Price', 'Please wait while we update the price...');

                    const formData = new FormData();
                    formData.append('order_id', orderId);
                    formData.append('price_type', priceType);
                    formData.append('price', price);

                    fetch('update_order_price.php', {
                        method: 'POST',
                        body: formData
                    })
                    .then(response => response.json())
                    .then(data => {
                        hideModal();
                        if (data.success) {
                            showSuccess('Price Updated', `${priceType.charAt(0).toUpperCase() + priceType.slice(1)} price has been updated successfully!`);
                        } else {
                            showError('Update Failed', data.message || 'Failed to update price.');
                        }
                    })
                    .catch(error => {
                        hideModal();
                        console.error('Error:', error);
                        showError('Update Failed', 'Network error. Please try again.');
                    });
                }
            );
        }

        // Logout confirmation
        function confirmLogout() {
            showConfirm(
                'Confirm Logout',
                'Are you sure you want to logout? You will need to login again to access the admin panel.',
                () => {
                    showLoading('Logging Out', 'Please wait...');
                    setTimeout(() => {
                        window.location.href = 'logout.php';
                    }, 1000);
                }
            );
        }
    </script>

</body>
</html>