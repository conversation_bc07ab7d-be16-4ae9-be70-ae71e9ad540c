<?php
session_start();
require_once 'config.php';
require_once 'affiliate_system.php';

// Check if user is admin
if (!isset($_SESSION["loggedin"]) || $_SESSION["loggedin"] !== true || !isset($_SESSION["is_admin"]) || $_SESSION["is_admin"] !== true) {
    header("location: ../index.php");
    exit;
}

$admin_user_id = $_SESSION['id'];
$action = $_POST['action'] ?? $_GET['action'] ?? '';

header('Content-Type: application/json');

switch ($action) {
    case 'get_pending_commissions':
        getPendingCommissions();
        break;
        
    case 'approve_commission':
        approveCommission($admin_user_id);
        break;
        
    case 'reject_commission':
        rejectCommission($admin_user_id);
        break;
        
    default:
        echo json_encode(['success' => false, 'message' => 'Invalid action']);
        break;
}

/**
 * Get all pending commissions
 */
function getPendingCommissions() {
    global $affiliate_system;
    
    $commissions = $affiliate_system->getPendingCommissions();
    
    echo json_encode([
        'success' => true,
        'commissions' => $commissions
    ]);
}

/**
 * Approve a commission
 */
function approveCommission($admin_user_id) {
    global $affiliate_system;
    
    $commission_id = $_POST['commission_id'] ?? '';
    $admin_notes = $_POST['admin_notes'] ?? '';
    
    if (empty($commission_id)) {
        echo json_encode(['success' => false, 'message' => 'Commission ID is required']);
        return;
    }
    
    $result = $affiliate_system->approveCommission($commission_id, $admin_user_id, $admin_notes);
    echo json_encode($result);
}

/**
 * Reject a commission
 */
function rejectCommission($admin_user_id) {
    global $affiliate_system;
    
    $commission_id = $_POST['commission_id'] ?? '';
    $admin_notes = $_POST['admin_notes'] ?? '';
    
    if (empty($commission_id)) {
        echo json_encode(['success' => false, 'message' => 'Commission ID is required']);
        return;
    }
    
    $result = $affiliate_system->rejectCommission($commission_id, $admin_user_id, $admin_notes);
    echo json_encode($result);
}
?>
