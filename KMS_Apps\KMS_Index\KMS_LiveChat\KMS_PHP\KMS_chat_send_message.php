<?php
header('Content-Type: application/json');
require_once __DIR__ . '/config.php';

$response = ['success' => false, 'message' => 'An unknown error occurred.'];
$link = get_db_connection();

try {
    $data = json_decode(file_get_contents('php://input'), true);
    $session_guid = $data['session_guid'] ?? null;
    $sender = $data['sender'] ?? null;
    $message = $data['message'] ?? null;

    if (empty($session_guid) || empty($sender) || empty($message)) {
        throw new Exception('Session GUID, sender, and message are required.');
    }
    
    if (!in_array($sender, ['user', 'admin'])) {
        throw new Exception('Invalid sender type.');
    }

    // Sanitize message
    $message = htmlspecialchars($message, ENT_QUOTES, 'UTF-8');

    // Get the chat session ID from the GUID
    $sql_get_id = "SELECT id FROM chat_sessions WHERE session_guid = ? AND status = 'active'";
    $stmt_get_id = mysqli_prepare($link, $sql_get_id);
    mysqli_stmt_bind_param($stmt_get_id, "s", $session_guid);
    mysqli_stmt_execute($stmt_get_id);
    $result = mysqli_stmt_get_result($stmt_get_id);
    $session = mysqli_fetch_assoc($result);
    mysqli_stmt_close($stmt_get_id);

    if (!$session) {
        throw new Exception('Active chat session not found.');
    }
    $chat_session_id = $session['id'];

    // Insert the message
    $sql_insert = "INSERT INTO chat_messages (chat_session_id, sender, message) VALUES (?, ?, ?)";
    $stmt_insert = mysqli_prepare($link, $sql_insert);
    
    if (!$stmt_insert) {
        throw new Exception('Failed to prepare statement: ' . mysqli_error($link));
    }

    mysqli_stmt_bind_param($stmt_insert, "iss", $chat_session_id, $sender, $message);
    
    if (mysqli_stmt_execute($stmt_insert)) {
        $response['success'] = true;
        $response['message'] = 'Message sent.';
    } else {
        throw new Exception('Failed to send message.');
    }

    mysqli_stmt_close($stmt_insert);

} catch (Exception $e) {
    $response['message'] = $e->getMessage();
    error_log("Chat Send Message Error: " . $e->getMessage());
} finally {
    if ($link) {
        close_db_connection($link);
    }
}

echo json_encode($response);
?>