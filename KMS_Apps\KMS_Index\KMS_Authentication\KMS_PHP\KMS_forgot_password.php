<?php
require_once 'db_connection.php';

header('Content-Type: application/json');

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['success' => false, 'message' => 'Invalid request method']);
    exit;
}

$username = trim($_POST['username'] ?? '');
$email = trim($_POST['email'] ?? '');
$phone = trim($_POST['phone'] ?? '');

// Validate input
if (empty($username) || empty($email) || empty($phone)) {
    echo json_encode(['success' => false, 'message' => 'All fields are required']);
    exit;
}

try {
    // Get database connection
    $link = get_db_connection();

    // Check if user exists with matching username, email, and phone
    $stmt = mysqli_prepare($link, "SELECT id, username, email, phone FROM users WHERE username = ? AND email = ? AND phone = ?");
    mysqli_stmt_bind_param($stmt, "sss", $username, $email, $phone);
    mysqli_stmt_execute($stmt);
    $result = mysqli_stmt_get_result($stmt);
    $user = mysqli_fetch_assoc($result);
    mysqli_stmt_close($stmt);

    if (!$user) {
        close_db_connection($link);
        echo json_encode(['success' => false, 'message' => 'No account found with the provided information']);
        exit;
    }

    // Generate reset token
    $reset_token = bin2hex(random_bytes(32));
    $expires_at = date('Y-m-d H:i:s', strtotime('+1 hour'));

    // Store reset token in database
    $stmt = mysqli_prepare($link, "INSERT INTO password_resets (user_id, token, expires_at) VALUES (?, ?, ?) ON DUPLICATE KEY UPDATE token = VALUES(token), expires_at = VALUES(expires_at)");
    mysqli_stmt_bind_param($stmt, "iss", $user['id'], $reset_token, $expires_at);
    mysqli_stmt_execute($stmt);
    mysqli_stmt_close($stmt);

    // Send reset email
    $reset_link = "https://kelvinkms.com/reset_password.php?token=" . $reset_token;
    
    $subject = "Password Reset Request - KelvinKMS.com";
    $message = "
    <html>
    <head>
        <title>Password Reset Request</title>
    </head>
    <body>
        <h2>Password Reset Request</h2>
        <p>Hello {$user['username']},</p>
        <p>You have requested to reset your password. Please click the link below to reset your password:</p>
        <p><a href='{$reset_link}' style='background-color: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Reset Password</a></p>
        <p>If you cannot click the link, copy and paste this URL into your browser:</p>
        <p>{$reset_link}</p>
        <p>This link will expire in 1 hour.</p>
        <p>If you did not request this password reset, please ignore this email.</p>
        <br>
        <p>Best regards,<br>KelvinKMS.com Team</p>
    </body>
    </html>
    ";

    $headers = "MIME-Version: 1.0" . "\r\n";
    $headers .= "Content-type:text/html;charset=UTF-8" . "\r\n";
    $headers .= 'From: <EMAIL>' . "\r\n";

    if (mail($email, $subject, $message, $headers)) {
        close_db_connection($link);
        echo json_encode(['success' => true, 'message' => 'Password reset email sent successfully']);
    } else {
        close_db_connection($link);
        echo json_encode(['success' => false, 'message' => 'Failed to send reset email']);
    }

} catch (Exception $e) {
    if (isset($link)) {
        close_db_connection($link);
    }
    echo json_encode(['success' => false, 'message' => 'Database error: ' . $e->getMessage()]);
}
?>