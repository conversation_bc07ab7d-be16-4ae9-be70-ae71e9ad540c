<?php
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Database configuration
define('DB_SERVER', 'localhost');
define('DB_USERNAME', 'root');
define('DB_PASSWORD', '');
define('DB_NAME', 'kelvinkms');

// <PERSON><PERSON><PERSON>
define('TWILIO_ACCOUNT_SID', '**********************************');
define('TWILIO_AUTH_TOKEN', '4229829dbc48bc47e9e314768728494f');
define('TWILIO_PHONE_NUMBER', '+***********');

// Set charset for proper UTF-8 support
define('DB_CHARSET', 'utf8mb4');

// Create connection with error handling
function get_db_connection() {
    $link = mysqli_connect(DB_SERVER, DB_USERNAME, DB_PASSWORD, DB_NAME);

    // Check connection
    if($link === false){
        error_log("Database connection failed: " . mysqli_connect_error());
        die("ERROR: Could not connect to database. Please try again later.");
    }

    // Set charset
    if (!mysqli_set_charset($link, DB_CHARSET)) {
        error_log("Error loading character set " . DB_CHARSET . ": " . mysqli_error($link));
    }

    return $link;
}

// Global connection for backward compatibility
$link = get_db_connection();

// Function to safely close database connection
function close_db_connection($connection) {
    if ($connection) {
        mysqli_close($connection);
    }
}

// Function to execute prepared statements safely
function execute_query($connection, $sql, $types = "", $params = []) {
    $stmt = mysqli_prepare($connection, $sql);
    if (!$stmt) {
        error_log("Prepare failed: " . mysqli_error($connection));
        return false;
    }

    if (!empty($params)) {
        mysqli_stmt_bind_param($stmt, $types, ...$params);
    }

    $result = mysqli_stmt_execute($stmt);
    if (!$result) {
        error_log("Execute failed: " . mysqli_stmt_error($stmt));
        mysqli_stmt_close($stmt);
        return false;
    }

    return $stmt;
}
?>