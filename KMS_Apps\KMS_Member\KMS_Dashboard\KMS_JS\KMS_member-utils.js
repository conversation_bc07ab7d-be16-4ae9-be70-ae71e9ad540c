// Member Utility Functions

// Custom Alert System
function showCustomAlert(type, title, message, buttons = null) {
    // Remove existing alert if any
    const existingAlert = document.querySelector('.custom-alert-overlay');
    if (existingAlert) {
        existingAlert.remove();
    }

    const alertOverlay = document.createElement('div');
    alertOverlay.className = 'custom-alert-overlay';
    alertOverlay.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.7);
        display: flex;
        justify-content: center;
        align-items: center;
        z-index: 10000;
    `;

    const alertBox = document.createElement('div');
    alertBox.className = 'custom-alert-box';
    alertBox.style.cssText = `
        background: linear-gradient(145deg, #2b9869, #1e6b47);
        border-radius: 15px;
        padding: 30px;
        max-width: 400px;
        width: 90%;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
        text-align: center;
        border: 2px solid #00ffff;
    `;

    const typeIcons = {
        'success': '✅',
        'error': '❌',
        'warning': '⚠️',
        'info': 'ℹ️'
    };

    const typeColors = {
        'success': '#4CAF50',
        'error': '#f44336',
        'warning': '#ff9800',
        'info': '#2196F3'
    };

    alertBox.innerHTML = `
        <div style="font-size: 48px; margin-bottom: 15px;">${typeIcons[type] || 'ℹ️'}</div>
        <h3 style="color: ${typeColors[type] || '#2196F3'}; margin: 0 0 15px 0; font-size: 24px;">${title}</h3>
        <p style="color: white; margin: 0 0 25px 0; line-height: 1.5; font-size: 16px;">${message}</p>
        <div class="alert-buttons">
            ${buttons ? buttons.map(btn => 
                `<button onclick="${btn.action}" style="
                    padding: 12px 24px;
                    margin: 0 8px;
                    border: none;
                    border-radius: 8px;
                    cursor: pointer;
                    font-weight: bold;
                    font-size: 14px;
                    background: ${btn.class === 'primary' ? '#007bff' : '#6c757d'};
                    color: white;
                    transition: all 0.3s ease;
                " onmouseover="this.style.opacity='0.8'" onmouseout="this.style.opacity='1'">${btn.text}</button>`
            ).join('') : 
            `<button onclick="hideCustomAlert()" style="
                padding: 12px 24px;
                border: none;
                border-radius: 8px;
                cursor: pointer;
                font-weight: bold;
                font-size: 14px;
                background: #007bff;
                color: white;
                transition: all 0.3s ease;
            " onmouseover="this.style.opacity='0.8'" onmouseout="this.style.opacity='1'">OK</button>`}
        </div>
    `;

    alertOverlay.appendChild(alertBox);
    document.body.appendChild(alertOverlay);

    // Auto-hide after 5 seconds for info alerts
    if (type === 'info' && !buttons) {
        setTimeout(() => {
            hideCustomAlert();
        }, 5000);
    }
}

function hideCustomAlert() {
    const overlay = document.querySelector('.custom-alert-overlay');
    if (overlay) {
        overlay.style.opacity = '0';
        setTimeout(() => {
            overlay.remove();
        }, 300);
    }
}

function showCustomConfirm(title, message, onConfirm, onCancel = null) {
    return showCustomAlert('warning', title, message, [
        { text: 'Cancel', class: 'secondary', action: onCancel || (() => hideCustomAlert()) },
        { text: 'Confirm', class: 'primary', action: onConfirm }
    ]);
}

// Replace all alert() calls with custom alerts
window.alert = function(message) {
    showCustomAlert('info', 'Notice', message);
};

window.confirm = function(message) {
    return new Promise((resolve) => {
        showCustomConfirm('Confirmation', message,
            () => { hideCustomAlert(); resolve(true); },
            () => { hideCustomAlert(); resolve(false); }
        );
    });
};

// Transfer functions
function openTransferModal() {
    const currentBalance = parseFloat(document.getElementById('walletBalance').textContent.replace('$', ''));

    if (currentBalance <= 0) {
        showCustomAlert('warning', 'Insufficient Balance', 'You need to have a positive balance to transfer credits.');
        return;
    }

    showModal('transfer-modal', '💸 Transfer Credits', `
        <div class="modal-form">
            <div class="form-group">
                <label style="color: #00ffff; font-size: 18px; margin-bottom: 15px; display: block;">Transfer Amount</label>
                <div class="amount-grid">
                    ${generateTransferAmountButtons(currentBalance)}
                </div>
                <div style="margin: 20px 0;">
                    <label style="color: #00ffff; margin-bottom: 10px; display: block;">Custom Amount</label>
                    <input type="number" id="custom-transfer-amount" min="1" max="${currentBalance}" step="0.01"
                           placeholder="Enter custom amount"
                           style="width: 100%; padding: 12px; border-radius: 8px; background: #333; color: white; border: 2px solid #555;"
                           onchange="selectCustomTransferAmount(this.value)">
                </div>
            </div>

            <div class="form-group">
                <label style="color: #00ffff; margin-bottom: 10px; display: block;">Recipient Username</label>
                <input type="text" id="transfer-username" placeholder="Enter username"
                       style="width: 100%; padding: 12px; border-radius: 8px; background: #333; color: white; border: 2px solid #555;">
            </div>

            <div class="form-group">
                <label style="color: #00ffff; margin-bottom: 10px; display: block;">Message (Optional)</label>
                <textarea id="transfer-message" placeholder="Add a message..."
                          style="width: 100%; height: 80px; padding: 12px; border-radius: 8px; background: #ffc000; color: black; border: 2px solid #555; resize: vertical;"></textarea>
            </div>

            <div id="transfer-summary" style="display: none; margin: 20px 0; padding: 15px; background: rgba(0,255,255,0.1); border-radius: 10px; border: 2px solid #00ffff;">
                <h4 style="color: #00ffff; margin: 0 0 10px 0;">Transfer Summary</h4>
                <div style="display: flex; justify-content: space-between; margin: 5px 0;">
                    <span>Amount:</span>
                    <span id="transfer-amount-display" style="color: #00ff00; font-weight: bold;">$0.00</span>
                </div>
                <div style="display: flex; justify-content: space-between; margin: 5px 0;">
                    <span>Recipient:</span>
                    <span id="transfer-recipient-display" style="color: #ffd700; font-weight: bold;">-</span>
                </div>
                <div style="display: flex; justify-content: space-between; margin: 5px 0;">
                    <span>Transfer Fee:</span>
                    <span id="transfer-fee-display" style="color: #ff9800;">$0.00</span>
                </div>
                <hr style="border: 1px solid rgba(255,255,255,0.2); margin: 10px 0;">
                <div style="display: flex; justify-content: space-between; margin: 5px 0; font-size: 18px;">
                    <span style="font-weight: bold;">Total Deducted:</span>
                    <span id="transfer-total-display" style="color: #ff6b6b; font-weight: bold; font-size: 20px;">$0.00</span>
                </div>
            </div>
        </div>
    `, [
        { text: 'Cancel', class: 'secondary', onclick: 'closeModal()' },
        { text: 'Send Transfer', class: 'primary', onclick: 'submitTransfer()' }
    ]);
}

function generateTransferAmountButtons(maxAmount) {
    const amounts = [10, 25, 50, 100, 250, 500];
    return amounts
        .filter(amount => amount <= maxAmount)
        .map(amount => `
            <div class="amount-btn" onclick="selectTransferAmount(${amount}, this)">
                <div class="amount-value">$${amount}</div>
                <div class="amount-label">Quick</div>
            </div>
        `).join('');
}

function selectTransferAmount(amount, button) {
    selectedTransferAmount = amount;

    // Update button states
    document.querySelectorAll('.amount-btn').forEach(btn => btn.classList.remove('selected'));
    button.classList.add('selected');

    // Clear custom input
    const customInput = document.getElementById('custom-transfer-amount');
    if (customInput) customInput.value = '';

    updateTransferSummary();
}

function selectCustomTransferAmount(amount) {
    const currentBalance = parseFloat(document.getElementById('walletBalance').textContent.replace('$', ''));

    if (amount && amount >= 1 && amount <= currentBalance) {
        selectedTransferAmount = parseFloat(amount);

        // Clear button selections
        document.querySelectorAll('.amount-btn').forEach(btn => btn.classList.remove('selected'));

        updateTransferSummary();
    }
}

function updateTransferSummary() {
    const summaryDiv = document.getElementById('transfer-summary');
    const recipientInput = document.getElementById('transfer-username');

    if (selectedTransferAmount > 0 && recipientInput && recipientInput.value.trim()) {
        const fee = Math.max(0.50, selectedTransferAmount * 0.01); // Minimum $0.50 or 1%
        const total = selectedTransferAmount + fee;

        document.getElementById('transfer-amount-display').textContent = '$' + selectedTransferAmount.toFixed(2);
        document.getElementById('transfer-recipient-display').textContent = recipientInput.value.trim();
        document.getElementById('transfer-fee-display').textContent = '$' + fee.toFixed(2);
        document.getElementById('transfer-total-display').textContent = '$' + total.toFixed(2);

        summaryDiv.style.display = 'block';
    } else {
        summaryDiv.style.display = 'none';
    }
}

// Add event listener for recipient input
document.addEventListener('input', function(e) {
    if (e.target && e.target.id === 'transfer-username') {
        updateTransferSummary();
    }
});

function openTransactionHistory() {
    showModal('history-modal', 'Transaction History', `
        <div class="history-container">
            <div id="transaction-list" style="max-height: 400px; overflow-y: auto;">
                <div style="text-align: center; padding: 20px;">
                    <div style="font-size: 18px; color: #ccc;">Loading transactions...</div>
                </div>
            </div>
        </div>
    `);

    // Load transaction history
    loadTransactionHistory();
}

function loadTransactionHistory() {
    fetch(`credit_wallet.php?action=get_transactions&PHPSESSID=${SESSION_ID}`, {
        credentials: 'same-origin'
    })
    .then(response => response.json())
    .then(data => {
        const container = document.getElementById('transaction-list');
        
        if (data.success && data.transactions && data.transactions.length > 0) {
            container.innerHTML = data.transactions.map(transaction => `
                <div style="background: rgba(0,0,0,0.1); padding: 15px; margin: 10px 0; border-radius: 8px; border-left: 4px solid ${getTransactionTypeColor(transaction.transaction_type)};">
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 8px;">
                        <div style="font-weight: bold; color: #00ffff;">${transaction.transaction_type.charAt(0).toUpperCase() + transaction.transaction_type.slice(1)}</div>
                        <div style="color: ${transaction.amount >= 0 ? '#4CAF50' : '#f44336'}; font-weight: bold;">
                            ${transaction.amount >= 0 ? '+' : ''}$${Math.abs(transaction.amount).toFixed(2)}
                        </div>
                    </div>
                    <div style="font-size: 14px; color: #ccc; margin-bottom: 5px;">
                        ${transaction.description || 'No description'}
                    </div>
                    <div style="font-size: 12px; color: #aaa;">
                        ${new Date(transaction.created_at).toLocaleString()}
                        ${transaction.payment_method ? ` | ${transaction.payment_method}` : ''}
                    </div>
                </div>
            `).join('');
        } else {
            container.innerHTML = '<div style="text-align: center; padding: 20px; color: #ccc;">No transactions found</div>';
        }
    })
    .catch(error => {
        console.error('Error loading transactions:', error);
        document.getElementById('transaction-list').innerHTML = '<div style="text-align: center; padding: 20px; color: #ff6b6b;">Error loading transactions</div>';
    });
}

function getTransactionTypeColor(type) {
    const colors = {
        'deposit': '#4CAF50',
        'withdrawal': '#f44336',
        'transfer_in': '#2196F3',
        'transfer_out': '#ff9800',
        'purchase': '#9C27B0',
        'refund': '#00bcd4'
    };
    return colors[type] || '#666';
}

// Heartbeat function to keep user online
function sendHeartbeat() {
    fetch('heartbeat.php', {
        method: 'POST',
        credentials: 'same-origin',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            timestamp: Date.now(),
            page: 'member'
        })
    })
    .catch(error => {
        // Silently handle heartbeat errors
        console.debug('Heartbeat error:', error);
    });
}

// Send heartbeat every 30 seconds
setInterval(sendHeartbeat, 30000);

// Send initial heartbeat
sendHeartbeat();
