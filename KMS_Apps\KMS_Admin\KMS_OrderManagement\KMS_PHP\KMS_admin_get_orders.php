<?php
session_start();
require_once 'config.php';

header('Content-Type: application/json');

// Admin Check
if (!isset($_SESSION["loggedin"]) || $_SESSION["loggedin"] !== true || !isset($_SESSION["is_admin"]) || $_SESSION["is_admin"] !== true) {
    echo json_encode(['success' => false, 'message' => 'Access denied. Please login as an admin.']);
    exit;
}

// Check if pc_orders table exists
$check_table_sql = "SHOW TABLES LIKE 'pc_orders'";
$table_result = mysqli_query($link, $check_table_sql);
$pc_orders_exists = mysqli_num_rows($table_result) > 0;

if (!$pc_orders_exists) {
    // Try to create the pc_orders table
    $create_table_sql = "CREATE TABLE `pc_orders` (
        `id` INT NOT NULL AUTO_INCREMENT,
        `user_id` INT NOT NULL,
        `order_details` TEXT NOT NULL,
        `status` ENUM(
            'Quote Requested',
            'Quote Provided',
            'Order Pending',
            'Payment Received',
            'Order In Progress',
            'Order Shipped',
            'Order Delivered',
            'Order Cancelled'
        ) NOT NULL DEFAULT 'Quote Requested',
        `final_price` DECIMAL(10, 2) NULL DEFAULT NULL,
        `estimated_completion_date` DATE NULL DEFAULT NULL,
        `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
        `updated_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        PRIMARY KEY (`id`),
        FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON DELETE CASCADE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4";
    
    if (mysqli_query($link, $create_table_sql)) {
        // Table created successfully, insert some test data
        $test_data_sql = "INSERT INTO pc_orders (user_id, order_details, status, final_price, estimated_completion_date) VALUES
        (1, 'Custom Gaming PC - RTX 4080, Intel i7-13700K, 32GB DDR5, 1TB NVMe SSD', 'Quote Requested', NULL, NULL),
        (1, 'Budget Office PC - Integrated Graphics, Intel i5-12400, 16GB DDR4, 500GB SSD', 'Quote Provided', 800.00, '2025-01-15')";
        mysqli_query($link, $test_data_sql); // Insert test data (ignore errors if user doesn't exist)
    } else {
        echo json_encode(['success' => false, 'message' => 'pc_orders table does not exist and could not be created. Error: ' . mysqli_error($link)]);
        close_db_connection($link);
        exit;
    }
}

// Fetch all orders, joining with users table to get user information
$sql = "SELECT
            po.id,
            po.user_id,
            u.username,
            u.nickname,
            po.order_details,
            po.status,
            po.final_price,
            po.estimated_completion_date,
            po.created_at,
            po.updated_at
        FROM pc_orders po
        JOIN users u ON po.user_id = u.id
        ORDER BY po.created_at DESC";

$stmt = mysqli_prepare($link, $sql);

if ($stmt) {
    mysqli_stmt_execute($stmt);
    $result = mysqli_stmt_get_result($stmt);
    $orders = mysqli_fetch_all($result, MYSQLI_ASSOC);
    mysqli_stmt_close($stmt);
    echo json_encode(['success' => true, 'orders' => $orders]);
} else {
    echo json_encode(['success' => false, 'message' => 'Failed to fetch orders. Error: ' . mysqli_error($link)]);
}

close_db_connection($link);

?>