<?php
/**
 * Commercial Payment Processor Integration
 * Supports: PayPal, Stripe, Square, Venmo, Zelle
 */

require_once 'config.php';

class PaymentProcessor {
    private $db;
    private $config;
    
    public function __construct($database_connection) {
        $this->db = $database_connection;
        $this->loadConfig();
    }
    
    private function loadConfig() {
        // Load payment processor configurations
        // In production, these should be stored securely (environment variables or encrypted config)
        $this->config = [
            'paypal' => [
                'client_id' => $_ENV['PAYPAL_CLIENT_ID'] ?? 'your_paypal_client_id',
                'client_secret' => $_ENV['PAYPAL_CLIENT_SECRET'] ?? 'your_paypal_client_secret',
                'sandbox' => $_ENV['PAYPAL_SANDBOX'] ?? true,
                'webhook_id' => $_ENV['PAYPAL_WEBHOOK_ID'] ?? 'your_webhook_id'
            ],
            'stripe' => [
                'publishable_key' => $_ENV['STRIPE_PUBLISHABLE_KEY'] ?? 'pk_test_your_key',
                'secret_key' => $_ENV['STRIPE_SECRET_KEY'] ?? 'sk_test_your_key',
                'webhook_secret' => $_ENV['STRIPE_WEBHOOK_SECRET'] ?? 'whsec_your_secret'
            ],
            'square' => [
                'application_id' => $_ENV['SQUARE_APPLICATION_ID'] ?? 'your_app_id',
                'access_token' => $_ENV['SQUARE_ACCESS_TOKEN'] ?? 'your_access_token',
                'location_id' => $_ENV['SQUARE_LOCATION_ID'] ?? 'your_location_id',
                'sandbox' => $_ENV['SQUARE_SANDBOX'] ?? true
            ]
        ];
    }
    
    /**
     * Create payment session for deposit
     */
    public function createDepositPayment($user_id, $amount, $payment_method, $total_amount) {
        try {
            switch ($payment_method) {
                case 'paypal':
                    return $this->createPayPalPayment($user_id, $amount, $total_amount);
                case 'stripe':
                    return $this->createStripePayment($user_id, $amount, $total_amount);
                case 'square':
                    return $this->createSquarePayment($user_id, $amount, $total_amount);
                default:
                    return $this->createManualPayment($user_id, $amount, $payment_method);
            }
        } catch (Exception $e) {
            error_log("Payment creation error: " . $e->getMessage());
            return [
                'success' => false,
                'message' => 'Payment processor temporarily unavailable. Please try again later.'
            ];
        }
    }
    
    /**
     * PayPal Payment Integration
     */
    private function createPayPalPayment($user_id, $amount, $total_amount) {
        $base_url = $this->config['paypal']['sandbox'] ? 
            'https://api.sandbox.paypal.com' : 'https://api.paypal.com';
        
        // Get access token
        $token = $this->getPayPalAccessToken();
        if (!$token) {
            throw new Exception('Failed to authenticate with PayPal');
        }
        
        // Create payment
        $payment_data = [
            'intent' => 'CAPTURE',
            'purchase_units' => [[
                'amount' => [
                    'currency_code' => 'USD',
                    'value' => number_format($total_amount, 2, '.', '')
                ],
                'description' => "KMS Credit Deposit - $" . number_format($amount, 2),
                'custom_id' => "deposit_{$user_id}_" . time()
            ]],
            'application_context' => [
                'return_url' => $this->getReturnUrl('paypal', 'success'),
                'cancel_url' => $this->getReturnUrl('paypal', 'cancel'),
                'brand_name' => 'KelvinKMS',
                'user_action' => 'PAY_NOW'
            ]
        ];
        
        $response = $this->makePayPalRequest('/v2/checkout/orders', $payment_data, $token);
        
        if ($response && isset($response['id'])) {
            // Store payment record
            $this->storePaymentRecord($user_id, $amount, 'paypal', $response['id'], 'pending');
            
            // Find approval URL
            $approval_url = '';
            foreach ($response['links'] as $link) {
                if ($link['rel'] === 'approve') {
                    $approval_url = $link['href'];
                    break;
                }
            }
            
            return [
                'success' => true,
                'payment_url' => $approval_url,
                'deposit_id' => $response['id']
            ];
        }
        
        throw new Exception('Failed to create PayPal payment');
    }
    
    /**
     * Stripe Payment Integration
     */
    private function createStripePayment($user_id, $amount, $total_amount) {
        require_once 'vendor/stripe/stripe-php/init.php'; // Include Stripe SDK
        
        \Stripe\Stripe::setApiKey($this->config['stripe']['secret_key']);
        
        try {
            $session = \Stripe\Checkout\Session::create([
                'payment_method_types' => ['card'],
                'line_items' => [[
                    'price_data' => [
                        'currency' => 'usd',
                        'product_data' => [
                            'name' => 'KMS Credit Deposit',
                            'description' => "Deposit $" . number_format($amount, 2) . " to your account"
                        ],
                        'unit_amount' => intval($total_amount * 100) // Stripe uses cents
                    ],
                    'quantity' => 1
                ]],
                'mode' => 'payment',
                'success_url' => $this->getReturnUrl('stripe', 'success') . '?session_id={CHECKOUT_SESSION_ID}',
                'cancel_url' => $this->getReturnUrl('stripe', 'cancel'),
                'client_reference_id' => "deposit_{$user_id}_" . time(),
                'metadata' => [
                    'user_id' => $user_id,
                    'deposit_amount' => $amount,
                    'type' => 'deposit'
                ]
            ]);
            
            // Store payment record
            $this->storePaymentRecord($user_id, $amount, 'stripe', $session->id, 'pending');
            
            return [
                'success' => true,
                'payment_url' => $session->url,
                'deposit_id' => $session->id
            ];
            
        } catch (\Stripe\Exception\ApiErrorException $e) {
            throw new Exception('Stripe error: ' . $e->getMessage());
        }
    }
    
    /**
     * Square Payment Integration
     */
    private function createSquarePayment($user_id, $amount, $total_amount) {
        $base_url = $this->config['square']['sandbox'] ? 
            'https://connect.squareupsandbox.com' : 'https://connect.squareup.com';
        
        $payment_data = [
            'idempotency_key' => uniqid("deposit_{$user_id}_"),
            'order' => [
                'location_id' => $this->config['square']['location_id'],
                'order' => [
                    'line_items' => [[
                        'name' => 'KMS Credit Deposit',
                        'quantity' => '1',
                        'base_price_money' => [
                            'amount' => intval($total_amount * 100), // Square uses cents
                            'currency' => 'USD'
                        ]
                    ]]
                ]
            ],
            'checkout_options' => [
                'redirect_url' => $this->getReturnUrl('square', 'success'),
                'ask_for_shipping_address' => false
            ]
        ];
        
        $headers = [
            'Authorization: Bearer ' . $this->config['square']['access_token'],
            'Content-Type: application/json',
            'Square-Version: 2023-10-18'
        ];
        
        $response = $this->makeHttpRequest($base_url . '/v2/online-checkout/payment-links', $payment_data, $headers);
        
        if ($response && isset($response['payment_link'])) {
            // Store payment record
            $this->storePaymentRecord($user_id, $amount, 'square', $response['payment_link']['id'], 'pending');
            
            return [
                'success' => true,
                'payment_url' => $response['payment_link']['url'],
                'deposit_id' => $response['payment_link']['id']
            ];
        }
        
        throw new Exception('Failed to create Square payment');
    }
    
    /**
     * Manual payment methods (Venmo, Zelle, etc.)
     */
    private function createManualPayment($user_id, $amount, $payment_method) {
        $deposit_id = 'manual_' . $user_id . '_' . time();
        
        // Store payment record as pending manual verification
        $this->storePaymentRecord($user_id, $amount, $payment_method, $deposit_id, 'pending_manual');
        
        $instructions = $this->getManualPaymentInstructions($payment_method, $amount);
        
        return [
            'success' => true,
            'deposit_id' => $deposit_id,
            'manual_payment' => true,
            'instructions' => $instructions
        ];
    }
    
    /**
     * Helper methods
     */
    private function getPayPalAccessToken() {
        $base_url = $this->config['paypal']['sandbox'] ? 
            'https://api.sandbox.paypal.com' : 'https://api.paypal.com';
        
        $auth = base64_encode($this->config['paypal']['client_id'] . ':' . $this->config['paypal']['client_secret']);
        
        $headers = [
            'Authorization: Basic ' . $auth,
            'Content-Type: application/x-www-form-urlencoded'
        ];
        
        $response = $this->makeHttpRequest($base_url . '/v1/oauth2/token', 'grant_type=client_credentials', $headers);
        
        return $response['access_token'] ?? null;
    }
    
    private function makePayPalRequest($endpoint, $data, $token) {
        $base_url = $this->config['paypal']['sandbox'] ? 
            'https://api.sandbox.paypal.com' : 'https://api.paypal.com';
        
        $headers = [
            'Authorization: Bearer ' . $token,
            'Content-Type: application/json'
        ];
        
        return $this->makeHttpRequest($base_url . $endpoint, json_encode($data), $headers);
    }
    
    private function makeHttpRequest($url, $data, $headers) {
        $ch = curl_init();
        curl_setopt_array($ch, [
            CURLOPT_URL => $url,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_POST => true,
            CURLOPT_POSTFIELDS => $data,
            CURLOPT_HTTPHEADER => $headers,
            CURLOPT_TIMEOUT => 30,
            CURLOPT_SSL_VERIFYPEER => true
        ]);
        
        $response = curl_exec($ch);
        $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        
        if ($http_code >= 200 && $http_code < 300) {
            return json_decode($response, true);
        }
        
        return false;
    }
    
    private function storePaymentRecord($user_id, $amount, $method, $external_id, $status) {
        $sql = "INSERT INTO payment_transactions (user_id, amount, payment_method, external_transaction_id, status, created_at) 
                VALUES (?, ?, ?, ?, ?, NOW())";
        
        $stmt = execute_query($this->db, $sql, "idsss", [$user_id, $amount, $method, $external_id, $status]);
        
        if ($stmt) {
            mysqli_stmt_close($stmt);
            return true;
        }
        
        return false;
    }
    
    private function getReturnUrl($method, $type) {
        $base_url = (isset($_SERVER['HTTPS']) ? 'https' : 'http') . '://' . $_SERVER['HTTP_HOST'];
        return $base_url . "/PHP/payment_callback.php?method={$method}&type={$type}";
    }
    
    private function getManualPaymentInstructions($method, $amount) {
        $instructions = [
            'venmo' => "Send $" . number_format($amount, 2) . " to @KelvinKMS-Official via Venmo. Include your username in the note.",
            'zelle' => "Send $" . number_format($amount, 2) . " to <EMAIL> via Zelle. Include your username in the memo.",
            'debit' => "Manual debit card processing will be handled by our support team. You will receive payment instructions via email."
        ];
        
        return $instructions[$method] ?? "Manual payment processing required. Support will contact you with instructions.";
    }
}
?>
