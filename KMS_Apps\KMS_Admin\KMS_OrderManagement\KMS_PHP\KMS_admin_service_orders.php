<?php
/**
 * Admin Service Orders Management
 */

session_start();
require_once 'config.php';
require_once 'functions.php';

// Check admin access
if (!isset($_SESSION["loggedin"]) || $_SESSION["loggedin"] !== true || !isset($_SESSION["is_admin"]) || !$_SESSION["is_admin"]) {
    header("location: login.php");
    exit;
}

// Get current language
$lang = $_SESSION['language'] ?? 'en';
$translations = loadTranslations($lang);

function t($key) {
    global $translations;
    return $translations[$key] ?? $key;
}
?>

<!DOCTYPE html>
<html lang="<?= $lang ?>">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= t('site_title') ?> - Service Orders Management</title>
    <link rel="stylesheet" href="../CSS/main.css">
    <style>
        body {
            font-family: Arial, sans-serif;
            background-color: #a48f19;
            color: white;
            margin: 0;
            padding: 20px;
        }
        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
            background-color: rgb(5 195 182);
            border-radius: 10px;
            margin-top: 20px;
            box-shadow: 0 2px 8px rgb(0 0 0);
        }
        
        .back-link {
            position: fixed;
            top: 10px;
            right: 10px;
            background-color: #00bcaa;
            color: white;
            border: 2px solid rgba(255, 255, 255, 0.3);
            padding: 5px 10px;
            border-radius: 10px;
            cursor: pointer;
            font-size: 16px;
            font-weight: bold;
            z-index: 1000;
            box-shadow: 0 2px 8px rgb(0 0 0);
            transition: all 0.3s ease;
            text-decoration: none;
        }
        
        .back-link:hover {
            background-color: #ffbf00ff;
            border-color: rgba(255, 255, 255, 0.3);
        }
        
        .page-header {
            text-align: center;
            color: #ffffff;
            margin-bottom: 30px;
        }
        
        .controls {
            margin-bottom: 20px;
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }
        
        .btn {
            padding: 5px 5px;
            border: none;
            border-radius: 10px;
            cursor: pointer;
            font-size: 14px;
            font-weight: bold;
            text-decoration: none;
            display: inline-block;
        }
        
        .btn-primary {
            background-color: rgb(253, 202, 0);
            color: white;
            border: 2px solid rgba(255, 255, 255, 0.3);
            box-shadow: 0 2px 8px rgb(0 0 0);
        }
        .btn-success {
            background-color: rgb(253, 202, 0);
            color: white;
            border: 2px solid rgba(255, 255, 255, 0.3);
            box-shadow: 0 2px 8px rgb(0 0 0);
        }
        .btn-warning {
            background-color: rgb(253, 202, 0);
            color: white;
            border: 2px solid rgba(255, 255, 255, 0.3);
            box-shadow: 0 2px 8px rgb(0 0 0);
        }
        .btn-danger {
            background-color: #dc3545;
            color: white;
            border: 2px solid rgba(255, 255, 255, 0.3);
            box-shadow: 0 2px 8px rgb(0 0 0);
        }
        .btn-secondary {
            background-color: #6c757d;
            color: white;
            border: 2px solid rgba(255, 255, 255, 0.3);
            box-shadow: 0 2px 8px rgb(0 0 0);
        }
        .btn-info {
            background-color: rgb(253, 202, 0);
            color: white;
            border: 2px solid rgba(255, 255, 255, 0.3);
            box-shadow: 0 2px 8px rgb(0 0 0);
        }
        
        .btn:hover {
            background-color: rgba(255, 255, 255, 0.5);
            border-color: rgba(255, 255, 255, 0.8);
        }
        
        .orders-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
            background: rgba(255,255,255,0.1);
            font-size: 14px;
        }
        
        .orders-table th,
        .orders-table td {
            padding: 10px;
            text-align: left;
            border-bottom: 1px solid rgba(255,255,255,0.2);
            color: white;
        }
        
        .orders-table th {
            background: rgba(253, 202, 0, 0.3);
            font-weight: bold;
            position: sticky;
            top: 0;
        }
        
        .orders-table tr:hover {
            background: rgba(255,255,255,0.1);
        }
        
        .status-pending { color: #ffa500; }
        .status-confirmed { color: #17a2b8; }
        .status-processing { color: #007bff; }
        .status-completed { color: #28a745; }
        .status-cancelled { color: #dc3545; }
        .status-refunded { color: #6c757d; }
        
        .payment-pending { color: #ffa500; }
        .payment-paid { color: #28a745; }
        .payment-refunded { color: #6c757d; }
        .payment-failed { color: #dc3545; }
        
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.8);
        }
        
        .modal-content {
            background-color: rgb(5 195 182);
            margin: 5% auto;
            padding: 20px;
            border-radius: 10px;
            width: 90%;
            max-width: 800px;
            color: white;
            max-height: 80vh;
            overflow-y: auto;
            box-shadow: 0 2px 8px rgb(0 0 0);
        }
        
        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }
        
        .close {
            color: #aaa;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
        }
        
        .close:hover {
            color: white;
        }
        
        .form-group {
            margin-bottom: 15px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 5px;
            color: #ffffff;
        }
        
        .form-group input,
        .form-group textarea,
        .form-group select {
            width: 100%;
            padding: 8px;
            border: 2px solid rgb(253, 202, 0);
            border-radius: 12px;
            background: rgb(255 194 0);
            color: #000;
            box-shadow: 0 2px 8px rgb(0 0 0);
        }
        
        .form-group textarea {
            height: 80px;
            resize: vertical;
        }
        
        .form-row {
            display: flex;
            gap: 15px;
        }
        
        .form-row .form-group {
            flex: 1;
        }
        
        .order-items {
            background: rgba(0,0,0,0.3);
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
        }
        
        .order-item {
            display: flex;
            justify-content: space-between;
            padding: 8px 0;
            border-bottom: 1px solid rgba(255,255,255,0.1);
        }
        
        .order-item:last-child {
            border-bottom: none;
        }
        
        .loading {
            text-align: center;
            color: #ffffff;
            padding: 20px;
        }
        
        .error {
            color: #dc3545;
            background: rgba(220, 53, 69, 0.1);
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        
        .success {
            color: #28a745;
            background: rgba(40, 167, 69, 0.1);
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        
        .table-container {
            max-height: 600px;
            overflow-y: auto;
        }
        
        .price-adjustment {
            background: rgba(255, 193, 7, 0.1);
            border: 1px solid #ffc107;
            border-radius: 4px;
            padding: 10px;
            margin: 10px 0;
        }
        
        .original-price {
            text-decoration: line-through;
            color: #999;
        }
        
        .adjusted-price {
            color: #ffc107;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <a href="admin.php" class="back-link">&larr; Back to Admin Panel</a>
        
        <div class="page-header">
            <h3>🛒 Service Orders Management</h3>
            <p>Manage all service orders and adjust pricing</p>
        </div>
        
        <div class="controls">
            <button class="btn btn-primary" onclick="loadOrders()">🔄 Refresh</button>
            <button class="btn btn-info" onclick="filterOrders('all')">All Orders</button>
            <button class="btn btn-warning" onclick="filterOrders('pending')">Pending</button>
            <button class="btn btn-success" onclick="filterOrders('paid')">Paid</button>
        </div>
        
        <div id="message"></div>
        
        <div class="table-container">
            <div id="ordersContainer">
                <div class="loading">Loading orders...</div>
            </div>
        </div>
    </div>
    
    <!-- Order Details Modal -->
    <div id="orderModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2 id="modalTitle">Order Details</h2>
                <span class="close" onclick="closeModal()">&times;</span>
            </div>
            
            <div id="orderDetails">
                <!-- Order details will be loaded here -->
            </div>
        </div>
    </div>
    
    <script>
        let currentOrders = [];
        let currentFilter = 'all';
        
        // Load orders on page load
        document.addEventListener('DOMContentLoaded', function() {
            loadOrders();
        });
        
        // Load all service orders
        function loadOrders() {
            fetch('service_orders_api.php?action=admin_get_orders')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        currentOrders = data.orders;
                        displayOrders(currentOrders);
                    } else {
                        showMessage('Error loading orders: ' + data.message, 'error');
                    }
                })
                .catch(error => {
                    showMessage('Error: ' + error.message, 'error');
                });
        }
        
        // Display orders in table
        function displayOrders(orders) {
            let html = `
                <table class="orders-table">
                    <thead>
                        <tr>
                            <th>Order #</th>
                            <th>Customer</th>
                            <th>Category</th>
                            <th>Items</th>
                            <th>Original Price</th>
                            <th>Final Price</th>
                            <th>Status</th>
                            <th>Payment</th>
                            <th>Date</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
            `;
            
            orders.forEach(order => {
                const itemCount = order.order_items ? order.order_items.length : 0;
                const originalPrice = parseFloat(order.final_price);
                const adjustedPrice = order.admin_adjusted_price ? parseFloat(order.admin_adjusted_price) : null;
                const displayPrice = adjustedPrice !== null ? adjustedPrice : originalPrice;
                
                html += `
                    <tr>
                        <td>${order.order_number}</td>
                        <td>${order.username || 'N/A'}<br><small>${order.email || ''}</small></td>
                        <td>${order.service_category}</td>
                        <td>${itemCount} item(s)</td>
                        <td>
                            ${adjustedPrice !== null ? `<span class="original-price">$${originalPrice.toFixed(2)}</span>` : `$${originalPrice.toFixed(2)}`}
                        </td>
                        <td>
                            ${adjustedPrice !== null ? `<span class="adjusted-price">$${adjustedPrice.toFixed(2)}</span>` : `$${displayPrice.toFixed(2)}`}
                        </td>
                        <td class="status-${order.status}">${order.status}</td>
                        <td class="payment-${order.payment_status}">${order.payment_status}</td>
                        <td>${new Date(order.created_at).toLocaleDateString()}</td>
                        <td>
                            <button class="btn btn-primary" onclick="viewOrder(${order.id})">👁️ View</button>
                            <button class="btn btn-warning" onclick="adjustPrice(${order.id})">💰 Adjust</button>
                        </td>
                    </tr>
                `;
            });
            
            html += '</tbody></table>';
            document.getElementById('ordersContainer').innerHTML = html;
        }
        
        // Filter orders
        function filterOrders(filter) {
            currentFilter = filter;
            let filteredOrders = currentOrders;
            
            if (filter === 'pending') {
                filteredOrders = currentOrders.filter(order => order.status === 'pending');
            } else if (filter === 'paid') {
                filteredOrders = currentOrders.filter(order => order.payment_status === 'paid');
            }
            
            displayOrders(filteredOrders);
        }
        
        // View order details
        function viewOrder(orderId) {
            const order = currentOrders.find(o => o.id == orderId);
            if (!order) return;
            
            let itemsHtml = '';
            if (order.order_items && order.order_items.length > 0) {
                order.order_items.forEach(item => {
                    itemsHtml += `
                        <div class="order-item">
                            <span>Service ID: ${item.service_id}</span>
                            <span>Qty: ${item.quantity}</span>
                        </div>
                    `;
                });
            }
            
            const originalPrice = parseFloat(order.final_price);
            const adjustedPrice = order.admin_adjusted_price ? parseFloat(order.admin_adjusted_price) : null;
            
            const detailsHtml = `
                <div class="order-info">
                    <div class="form-row">
                        <div class="form-group">
                            <label>Order Number:</label>
                            <input type="text" value="${order.order_number}" readonly>
                        </div>
                        <div class="form-group">
                            <label>Customer:</label>
                            <input type="text" value="${order.username} (${order.email})" readonly>
                        </div>
                    </div>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label>Category:</label>
                            <input type="text" value="${order.service_category}" readonly>
                        </div>
                        <div class="form-group">
                            <label>Status:</label>
                            <select id="order-status">
                                <option value="pending" ${order.status === 'pending' ? 'selected' : ''}>Pending</option>
                                <option value="confirmed" ${order.status === 'confirmed' ? 'selected' : ''}>Confirmed</option>
                                <option value="processing" ${order.status === 'processing' ? 'selected' : ''}>Processing</option>
                                <option value="completed" ${order.status === 'completed' ? 'selected' : ''}>Completed</option>
                                <option value="cancelled" ${order.status === 'cancelled' ? 'selected' : ''}>Cancelled</option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label>Order Items:</label>
                        <div class="order-items">
                            ${itemsHtml || 'No items found'}
                        </div>
                    </div>
                    
                    <div class="price-adjustment">
                        <div class="form-row">
                            <div class="form-group">
                                <label>Original Price:</label>
                                <input type="text" value="$${originalPrice.toFixed(2)}" readonly>
                            </div>
                            <div class="form-group">
                                <label>Adjusted Price:</label>
                                <input type="number" id="adjusted-price" step="0.01" min="0" 
                                       value="${adjustedPrice !== null ? adjustedPrice : originalPrice}" 
                                       placeholder="Enter new price">
                            </div>
                        </div>
                        
                        <div class="form-group">
                            <label>Adjustment Reason:</label>
                            <textarea id="adjustment-reason" placeholder="Reason for price adjustment">${order.admin_adjustment_reason || ''}</textarea>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label>Notes:</label>
                        <textarea readonly>${order.notes || 'No notes'}</textarea>
                    </div>
                    
                    <div class="controls">
                        <button class="btn btn-success" onclick="updateOrder(${order.id})">💾 Update Order</button>
                        <button class="btn btn-secondary" onclick="closeModal()">❌ Close</button>
                    </div>
                </div>
            `;
            
            document.getElementById('orderDetails').innerHTML = detailsHtml;
            document.getElementById('modalTitle').textContent = `Order #${order.order_number}`;
            document.getElementById('orderModal').style.display = 'block';
        }
        
        // Adjust price shortcut
        function adjustPrice(orderId) {
            viewOrder(orderId);
            // Focus on the adjusted price field
            setTimeout(() => {
                const priceField = document.getElementById('adjusted-price');
                if (priceField) {
                    priceField.focus();
                    priceField.select();
                }
            }, 100);
        }
        
        // Update order
        function updateOrder(orderId) {
            const status = document.getElementById('order-status').value;
            const adjustedPrice = document.getElementById('adjusted-price').value;
            const adjustmentReason = document.getElementById('adjustment-reason').value;
            
            const formData = new FormData();
            formData.append('action', 'admin_update_order');
            formData.append('order_id', orderId);
            formData.append('status', status);
            formData.append('admin_adjusted_price', adjustedPrice);
            formData.append('admin_adjustment_reason', adjustmentReason);
            
            fetch('service_orders_api.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showMessage(data.message, 'success');
                    closeModal();
                    loadOrders();
                } else {
                    showMessage('Error: ' + data.message, 'error');
                }
            })
            .catch(error => {
                showMessage('Error: ' + error.message, 'error');
            });
        }
        
        // Close modal
        function closeModal() {
            document.getElementById('orderModal').style.display = 'none';
        }
        
        // Show message
        function showMessage(message, type) {
            const messageDiv = document.getElementById('message');
            messageDiv.innerHTML = `<div class="${type}">${message}</div>`;
            setTimeout(() => {
                messageDiv.innerHTML = '';
            }, 5000);
        }
        
        // Close modal when clicking outside
        window.onclick = function(event) {
            const modal = document.getElementById('orderModal');
            if (event.target == modal) {
                closeModal();
            }
        }
    </script>
</body>
</html>
