<?php
/**
 * Payment Callback Handler
 * Handles success/cancel callbacks from payment processors
 */

session_start();
require_once 'config.php';
require_once 'credit_system.php';

$method = $_GET['method'] ?? '';
$type = $_GET['type'] ?? '';

// Redirect to member page with appropriate message
$redirect_url = 'member.php';

switch ($method) {
    case 'paypal':
        handlePayPalCallback($type);
        break;
    case 'stripe':
        handleStripeCallback($type);
        break;
    case 'square':
        handleSquareCallback($type);
        break;
    default:
        $redirect_url .= '?payment=error&message=' . urlencode('Invalid payment method');
        break;
}

header("Location: $redirect_url");
exit;

function handlePayPalCallback($type) {
    global $redirect_url;
    
    if ($type === 'success') {
        $token = $_GET['token'] ?? '';
        $payer_id = $_GET['PayerID'] ?? '';
        
        if ($token && $payer_id) {
            // Verify and capture payment
            $result = capturePayPalPayment($token, $payer_id);
            
            if ($result['success']) {
                $redirect_url .= '?payment=success&message=' . urlencode('Payment completed successfully! Your credits will be added shortly.');
            } else {
                $redirect_url .= '?payment=error&message=' . urlencode('Payment verification failed. Please contact support.');
            }
        } else {
            $redirect_url .= '?payment=error&message=' . urlencode('Invalid payment parameters.');
        }
    } else {
        $redirect_url .= '?payment=cancelled&message=' . urlencode('Payment was cancelled.');
    }
}

function handleStripeCallback($type) {
    global $redirect_url;
    
    if ($type === 'success') {
        $session_id = $_GET['session_id'] ?? '';
        
        if ($session_id) {
            // Verify Stripe session
            $result = verifyStripeSession($session_id);
            
            if ($result['success']) {
                $redirect_url .= '?payment=success&message=' . urlencode('Payment completed successfully! Your credits will be added shortly.');
            } else {
                $redirect_url .= '?payment=error&message=' . urlencode('Payment verification failed. Please contact support.');
            }
        } else {
            $redirect_url .= '?payment=error&message=' . urlencode('Invalid session ID.');
        }
    } else {
        $redirect_url .= '?payment=cancelled&message=' . urlencode('Payment was cancelled.');
    }
}

function handleSquareCallback($type) {
    global $redirect_url;
    
    if ($type === 'success') {
        $checkout_id = $_GET['checkoutId'] ?? '';
        
        if ($checkout_id) {
            // Verify Square payment
            $result = verifySquarePayment($checkout_id);
            
            if ($result['success']) {
                $redirect_url .= '?payment=success&message=' . urlencode('Payment completed successfully! Your credits will be added shortly.');
            } else {
                $redirect_url .= '?payment=error&message=' . urlencode('Payment verification failed. Please contact support.');
            }
        } else {
            $redirect_url .= '?payment=error&message=' . urlencode('Invalid checkout ID.');
        }
    } else {
        $redirect_url .= '?payment=cancelled&message=' . urlencode('Payment was cancelled.');
    }
}

function capturePayPalPayment($token, $payer_id) {
    // Implementation would verify and capture PayPal payment
    // This is a simplified version - full implementation would use PayPal SDK
    
    global $link;
    
    try {
        // Update payment record
        $sql = "UPDATE payment_transactions SET 
                status = 'completed', 
                payer_id = ?, 
                completed_at = NOW() 
                WHERE external_transaction_id = ? AND status = 'pending'";
        
        $stmt = execute_query($link, $sql, "ss", [$payer_id, $token]);
        
        if ($stmt) {
            $affected_rows = mysqli_stmt_affected_rows($stmt);
            mysqli_stmt_close($stmt);
            
            if ($affected_rows > 0) {
                // Get payment details and add credits
                return processCompletedPayment($token);
            }
        }
        
        return ['success' => false, 'message' => 'Payment record not found'];
        
    } catch (Exception $e) {
        error_log("PayPal capture error: " . $e->getMessage());
        return ['success' => false, 'message' => 'Payment processing error'];
    }
}

function verifyStripeSession($session_id) {
    // Implementation would verify Stripe session
    // This is a simplified version - full implementation would use Stripe SDK
    
    global $link;
    
    try {
        // Update payment record
        $sql = "UPDATE payment_transactions SET 
                status = 'completed', 
                completed_at = NOW() 
                WHERE external_transaction_id = ? AND status = 'pending'";
        
        $stmt = execute_query($link, $sql, "s", [$session_id]);
        
        if ($stmt) {
            $affected_rows = mysqli_stmt_affected_rows($stmt);
            mysqli_stmt_close($stmt);
            
            if ($affected_rows > 0) {
                // Get payment details and add credits
                return processCompletedPayment($session_id);
            }
        }
        
        return ['success' => false, 'message' => 'Payment record not found'];
        
    } catch (Exception $e) {
        error_log("Stripe verification error: " . $e->getMessage());
        return ['success' => false, 'message' => 'Payment processing error'];
    }
}

function verifySquarePayment($checkout_id) {
    // Implementation would verify Square payment
    // This is a simplified version - full implementation would use Square SDK
    
    global $link;
    
    try {
        // Update payment record
        $sql = "UPDATE payment_transactions SET 
                status = 'completed', 
                completed_at = NOW() 
                WHERE external_transaction_id = ? AND status = 'pending'";
        
        $stmt = execute_query($link, $sql, "s", [$checkout_id]);
        
        if ($stmt) {
            $affected_rows = mysqli_stmt_affected_rows($stmt);
            mysqli_stmt_close($stmt);
            
            if ($affected_rows > 0) {
                // Get payment details and add credits
                return processCompletedPayment($checkout_id);
            }
        }
        
        return ['success' => false, 'message' => 'Payment record not found'];
        
    } catch (Exception $e) {
        error_log("Square verification error: " . $e->getMessage());
        return ['success' => false, 'message' => 'Payment processing error'];
    }
}

function processCompletedPayment($transaction_id) {
    global $link;
    
    try {
        // Get payment details
        $sql = "SELECT user_id, amount FROM payment_transactions WHERE external_transaction_id = ? AND status = 'completed'";
        $stmt = execute_query($link, $sql, "s", [$transaction_id]);
        
        if (!$stmt) {
            return ['success' => false, 'message' => 'Database error'];
        }
        
        $result = mysqli_stmt_get_result($stmt);
        $payment = mysqli_fetch_assoc($result);
        mysqli_stmt_close($stmt);
        
        if (!$payment) {
            return ['success' => false, 'message' => 'Payment not found'];
        }
        
        // Add credits to user account
        $credit_system = new KMSCreditSystem($link);
        $add_result = $credit_system->addCredit(
            $payment['user_id'],
            $payment['amount'],
            'deposit',
            "Payment deposit via " . $transaction_id,
            $transaction_id
        );
        
        if ($add_result['success']) {
            return ['success' => true, 'message' => 'Credits added successfully'];
        } else {
            return ['success' => false, 'message' => 'Failed to add credits'];
        }
        
    } catch (Exception $e) {
        error_log("Payment processing error: " . $e->getMessage());
        return ['success' => false, 'message' => 'Payment processing error'];
    }
}
?>
