<?php
session_start();

// --- Update user's last seen timestamp on logout ---
// Keep the last_seen timestamp instead of setting it to NULL
// This allows the time-based online detection to work properly
if (isset($_SESSION['username'])) {
    require_once __DIR__ . '/config.php';

    $username = $_SESSION['username'];
    // Update last_seen to current time on logout
    // The online detection will use time-based logic to determine if user is still online
    $sql = "UPDATE users SET last_seen = NOW() WHERE username = ?";

    $stmt = mysqli_prepare($link, $sql);
    if ($stmt) {
        mysqli_stmt_bind_param($stmt, "s", $username);
        mysqli_stmt_execute($stmt);
        mysqli_stmt_close($stmt);
    }
    // Close the connection if it was opened
    if(isset($link)) {
        close_db_connection($link);
    }
}

// Unset all session variables
$_SESSION = array();

// Destroy the session cookie
if (ini_get("session.use_cookies")) {
    $params = session_get_cookie_params();
    setcookie(session_name(), '', time() - 42000,
        $params["path"], $params["domain"],
        $params["secure"], $params["httponly"]
    );
}

// Destroy the session
session_destroy();

// Redirect to index page
header("location: ../index.php");
exit;
?>